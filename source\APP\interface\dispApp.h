/******************************************************************************
 *    (c) Copyright 2016, SheWei Electronics Co.,Ltd.
 *    All Rights Reserved
 *
 *    Filename:      disApp.h
 *    Describe:      显示服务模块
 *
 *    Device:
 *    Compiler:
 *
 *    Created on:
 *    Modify record:
 *
 *******************************************************************************/

#ifndef _DISPAPP_H_
#define _DISPAPP_H_

/* Includes ------------------------------------------------------------------*/
#include "typedef.h"
#include "app_config.h"
#include "DLT_698_typedef.h"

/*Private define--------------------------------------------*/
#define DISP_MAX_DATA_LEN       128
#define DISP_AUTO_ENENT_ENABLE  SUPPORT_DISP_AUTO_ENENT


/*Private typedef--------------------------------------------*/
typedef uint16_t DISP_IDX_TYPE;

typedef enum
{
    DISP_AUTO = 0,    // 自动轮显模式
    DISP_MANUAL,      // 手动轮显模式
    DISP_RECHARGE,    // 充值显示模式
    DISP_PWDN,        // 掉电显示模式
#if USE_KEYBOARD
    DISP_KEYIN,       // 键盘输入模式
    DISP_KEYOUT,      // 键盘输出模式
#endif
    DISP_MESSAGE,     // 紧急显示模式(显示一段时间)
    DISP_LOCKING,     // 静态显示模式(一直显示直至状态解除)
} disp_mode_t;

typedef enum
{
    PD_DISP_CLOSE = 0,
    PD_DISP_COV_OPN,
    PD_COV_OPN_DISPLAYING,
    PD_DISP_KEY_IN,
    PD_DISP_DELAY,
} diap_pwrdwn_state_t;

typedef enum
{
    MESS_MODE_INS = 0,    // 紧急消息
    MESS_MODE_EVE,        // 事件消息
} mess_mode_s;

/* 显示格式定义 */
typedef enum
{
    DF_UNDEFINED = 0,   // 无效格式
    DF_DECIMAL_0,       // 显示不带小数点: xxxxxx
    DF_DECIMAL_1,       // 显示带1个小数点: xxxxx.x
    DF_DECIMAL_2,       // 显示带2个小数点: xxxx.xx
    DF_DECIMAL_3,       // 显示带3个小数点: xxx.xxx
    DF_DECIMAL_4,       // 显示带4个小数点: xx.xxxx
    DF_ENERGY,          // 电能显示 - 小数位数按照参数配置
    DF_POWER,           // 功率显示 - 小数位数按照参数配置
    DF_CURRENT,         // 电流显示 - 小数位数按照参数配置
    DF_VOLTAGE,         // 电压显示 - 小数位数按照参数配置
    DF_MULTIPY_CT,      // 乘以电流变比
    DF_MULTIPY_PT,      // 乘以电压变比
    DF_MULTIPY_PT_CT,   // 乘以电流*电压变比
    DF_HEX_STRING,      // HEX串   从左到右，由低字节到高字节显示
    DF_CHR_STRING,      // 字符串 ascii 从左到右，由低字节到高字节显示
    DF_DATE,            // 日期格式: YYYY-MM-DD
    DF_TIME,            // 时间格式: hh:mm:ss
    DF_DATE_TIME,       // 日期时间分屏格式
} dis_dat_typ_t;

typedef union
{
    struct
    {
        uint32_t open_cov   : 1;        // 开盖
        uint32_t p_over     : 1;        // 超功率拉闸
    };
    uint32_t lword;
} disp_req_status_s;

typedef struct
{
    uint8_t  lcd_pow_off_time;    // 液晶掉电显示时间
    uint8_t  auto_Period;         // 自动轮显周期
    uint16_t mode_outtime;        // 模式切换时间
    uint8_t  alarm_time;          // 报警时间
    uint8_t  backlight_time;      // 背光时间
    uint8_t  pwdn_auto_Period;    // 掉电轮显周期
    uint8_t  pwon_all_disp;       // 上电全显时间
} disp_set_time_s;

/* 所有小数位显示最大4位小数 */
typedef union
{
    struct
    {
        uint32_t energy_lz      : 1;    // 电能值显示前导零
        uint32_t power_lz       : 1;    // 功率值显示前导零
        uint32_t current_lz     : 1;    // 电流值显示前导零
        uint32_t voltage_lz     : 1;    // 电压值显示前导零

        uint32_t energy_decimal : 3;    // 电能显示小数位数(0-6指示小数位数，7不覆盖显示对象的显示格式小数点属性，)
        uint32_t power_decimal  : 3;    // 功率，需量显示小数位数(0-6指示小数位数，7不覆盖显示对象的显示格式小数点属性，)
        uint32_t current_decimal: 3;    // 电流显示小数位数(0-6指示小数位数，7不覆盖显示对象的显示格式小数点属性，)
        uint32_t voltage_decimal: 3;    // 电压显示小数位数(0-6指示小数位数，7不覆盖显示对象的显示格式小数点属性，)
        uint32_t power_dimen    : 2;    // 功率，需量显示单位(0 W,1 KW, 2 MW)
        uint32_t energy_dimen   : 2;    // 电能显示单位(0 WH, 1 KWH, 2 MWH)
        uint32_t current_dimen  : 2;    // 电流显示单位(0 A,1 KA)
        uint32_t voltage_dimen  : 2;    // 电压显示单位(0 V, 1 KV)
    };
    uint32_t val;
} disp_spe_format_s;

typedef struct
{
    disp_spe_format_s spe_format;
} disp_special_s;

/*数据结构体*/
typedef struct
{
    uint16_t          crc;
    uint8_t           chk;
    disp_req_status_s req_filter;
    disp_set_time_s   time;
    disp_special_s    spe;
} disp_para_s;

/// @brief 显示对象数据结构
typedef struct
{
    uint32_t id;            // 数据ID
    union{
        uint32_t format;    // 显示格式
        struct 
        {
            DLT698_data_type_t  dtyp;       // 数据类型
            dis_dat_typ_t       df;         // 显示类型
            class_unit_t        unit;       // 单位
            uint8_t             num;        // 屏数 比如最大需量有两屏
        }; 
    };
} disp_obj_s;

typedef struct
{
    uint8_t sign;                       // 符号位
    uint8_t unit;                       // 数值单位
    int8_t  scaler;                     // 基于当前数值单位的量纲
    uint8_t data_len;                   // 数据长度
    uint8_t data[DISP_MAX_DATA_LEN];    // 数据缓存buffer
} disp_obj_api_s;

/* 显示列表类型定义 */
typedef enum
{
    DISP_LIST1,    // 显示列表类型1 auto
    DISP_LIST2,    // 显示列表类型2 manual
    DISP_LIST3,    // 显示列表类型3 power down
    DISP_LIST_NUM
} TYPE_DISP_LIST;

/* 显示清单索引列表定义,    用于显示项编程存储              */
typedef struct
{
    uint16_t      crc;
    uint16_t      chk;
    DISP_IDX_TYPE num;    // 索引个数
    DISP_IDX_TYPE buf[DISP_ID_MAX_NUM];
} disp_list_s;

typedef uint16_t DISP_STUS;
#define STUS_DISP_PARA_CHANGE (1U << 0)      // 显示相关参数修改
#define STUS_DISP_AUTO_TIME_SET (1U << 1)    // 自动轮显时长修改
#define STUS_DISP_BUTT_TIME_SET (1U << 2)    // 按键轮显时长修改
#define STUS_DISP_AUTO_LIST_SET (1U << 3)    // 自动轮显列表修改
#define STUS_DISP_BUTT_LIST_SET (1U << 4)    // 按键轮显列表修改


/*-Export functions------------------------------------*/
struct display_s
{
    void (*reset)(uint8_t type);
    const disp_para_s *(*para_get)(void);
    bool (*para_set)(uint16_t ofst, const void *val, uint16_t len);
    bool (*state_query)(DISP_STUS state);
    void (*state_set)(DISP_STUS state);
    void (*state_clr)(void);
    bool (*id_list_set)(TYPE_DISP_LIST type, const uint32_t *id_list, uint16_t num);
    bool (*id_set)(TYPE_DISP_LIST type, uint16_t index, uint32_t id);
    uint16_t (*id_list_get)(TYPE_DISP_LIST type, uint32_t *id_list);
    uint16_t (*id_list_get_num)(TYPE_DISP_LIST type);
    uint8_t  (*id_get)(TYPE_DISP_LIST type, uint16_t index, uint32_t *id);
    bool (*id_check)(uint32_t id);
};
extern const struct display_s display;
extern const struct app_task_t display_task;
#endif /* _DISPAPP_H_ */
