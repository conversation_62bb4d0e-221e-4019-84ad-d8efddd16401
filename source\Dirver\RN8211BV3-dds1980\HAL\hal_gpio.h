/**
 ******************************************************************************
 * @file    hal_gpio.h
 * <AUTHOR>
 * @version V1.0
 * @date    2024-08-05
 * @brief   This file contains all the functions prototypes for the GPIO
 * @note
 *
 ******************************************************************************
 *
 * @note
 * Copyright (C) 2016 SheWei Electrics Co.,Ltd. All rights reserved..
 *
 ******************************************************************************/
// #ifdef __cplusplus
//  extern "C" {
// #endif

#ifndef __HAL_GPIO_H
#define __HAL_GPIO_H

/* Includes ------------------------------------------------------------------*/
#include "hal_def.h"

/*
 * @brief 系统引脚功能定义:
   -o  输出
   -i  输入
   -ii 中断输入
   -io 双向IO
   -fa 摸拟输入
   -fi 外设功能输入
   -fo 外设功能输出
   -ff 外设功能双向输入/出
   -fw PWM输出
*/
#define PIN_TZ_LED PIN_0_0
#define PIN_PHY_LED PIN_0_1
#define PIN_LVD_3V0 PIN_0_2
#define PIN_LVD_12V PIN_0_3
#define PIN_RELAY_ON PIN_1_0  
#define PIN_RELAY_OFF PIN_1_1 
#define PIN_485_UART0_RXD PIN_2_0
#define PIN_485_UART0_TXD PIN_2_1
#define PIN_IDEL_22 PIN_2_2
#define PIN_BT_PWR PIN_2_3
#define PIN_SWD_CLK PIN_2_4
#define PIN_SWD_IO PIN_2_5
#define PIN_ISP PIN_3_0
#define PIN_3V3_EP PIN_3_2
#define PIN_PF_CF PIN_5_0
#define PIN_I2C_SCL PIN_5_2
#define PIN_I2C_SDA PIN_5_3
#define PIN_PHY_UART5_RXD PIN_5_4
#define PIN_PHY_UART5_TXD PIN_5_5
#define PIN_LCD_COM0 PIN_12_0
#define PIN_LCD_COM1 PIN_12_1
#define PIN_LCD_COM2 PIN_12_2
#define PIN_LCD_COM3 PIN_12_3
#define PIN_LCD_SEG1 PIN_12_4
#define PIN_LCD_SEG2 PIN_12_5
#define PIN_LCD_SEG3 PIN_12_6
#define PIN_LCD_SEG4 PIN_12_7
#define PIN_LCD_SEG5 PIN_3_6
#define PIN_LCD_SEG6 PIN_3_7
#define PIN_LCD_SEG7 PIN_9_3
#define PIN_LCD_SEG8 PIN_9_2
#define PIN_LCD_SEG9 PIN_9_1
#define PIN_LCD_SEG10 PIN_9_0
#define PIN_LCD_SEG11 PIN_8_7
#define PIN_LCD_SEG12 PIN_8_6
#define PIN_LCD_SEG13 PIN_8_5
#define PIN_LCD_SEG14 PIN_8_4
#define PIN_LCD_SEG15 PIN_8_3
#define PIN_LCD_SEG16 PIN_8_2
#define PIN_LCD_SEG17 PIN_8_1
#define PIN_LCD_SEG18 PIN_8_0

#define PIN_LCD_VA PIN_13_0
#define PIN_LCD_VB PIN_13_1
#define PIN_LCD_VC PIN_13_2
//#define PIN_LCD_VD PIN_13_3

#define PIN_LCD_VP1 PIN_13_4
#define PIN_LCD_VP2 PIN_13_5


/* Exported macro ------------------------------------------------------------*/
/// 以下根据芯片手册寄存器描述进行配置。当没有相应功能时，应定义为空，不能直接注释掉

/* @attention 时间问题，官方库配置GPIO */

/* @brief 寄存器操作方式配置GPIO输入 */
#define HAL_GPIO_DIR_IN(pin) (LL_GPIO_CfgDirMode(pin, GPIO_MODE_IN))
/* @brief 寄存器操作方式配置GPIO输出 */
#define HAL_GPIO_DIR_OUT(pin) (LL_GPIO_CfgDirMode(pin, GPIO_MODE_OUT))

/* @brief 寄存器操作方式配置获取GPIO输入电平 */
#define HAL_GPIO_IN_GET(pin) (LL_GPIO_ReadPin(pin))
/* @brief 寄存器操作方式配置获取GPIO输出电平 */
#define HAL_GPIO_OUT_GET(pin) (LL_GPIO_ReadPin(pin))

/* @brief 寄存器操作方式配置GPIO输出低电平 */
#define HAL_GPIO_OUT_RST(pin) (LL_GPIO_SetPin(pin,Low_Level))
/* @brief 寄存器操作方式配置GPIO输出高电平 */
#define HAL_GPIO_OUT_SET(pin) (LL_GPIO_SetPin(pin,High_Level))

/* @brief 寄存器操作方式配置GPIO输出翻转电平 */
#define HAL_GPIO_OUT_REV(pin) (HAL_GPIO_OUT_GET(pin) ? HAL_GPIO_OUT_RST(pin) : HAL_GPIO_OUT_SET(pin))
/* @brief 寄存器操作方式配置GPIO上拉使能 */
#define HAL_GPIO_PTUP_EN(pin) (LL_GPIO_CfgPullMode(pin,Pull_ON))
/* @brief 寄存器操作方式配置GPIO上拉关闭 */
#define HAL_GPIO_PTUP_DIS(pin) (LL_GPIO_CfgPullMode(pin,Pull_OFF))

/* @brief 寄存器操作方式配置GPIO下拉使能 */
#define HAL_GPIO_PTPD_EN(pin) ((void)pin)
/* @brief 寄存器操作方式配置GPIO下拉拉关闭 */
#define HAL_GPIO_PTPD_DIS(pin) ((void)pin)

/* @brief 寄存器操作方式配置GPIO开漏关闭 */
#define HAL_GPIO_PTOD_DIS(pin) HAL_GPIO_PTUP_EN(pin)
/* @brief 寄存器操作方式配置GPIO开漏开启 */
#define HAL_GPIO_PTOD_EN(pin) HAL_GPIO_PTUP_DIS(pin)

/* @brief 寄存器操作方式配置GPIO关闭高驱动能力 */
#define HAL_GPIO_PTDR_DIS(pin) ((void)pin)
/* @brief 寄存器操作方式配置GPIO开启高驱动能力 */
#define HAL_GPIO_PTDR_EN(pin) ((void)pin)

/* @brief 寄存器操作方式配置GPIO为模拟端口 */
#define HAL_GPIO_AIN(pin) ((void)pin)
/* @brief 寄存器操作方式配置GPIO为数字端口 */
#define HAL_GPIO_ADS(pin) ((void)pin)

/* @brief 获取GPIO寄存器名称及管脚编号 */
#define HAL_GPIO_PORT(port, pin) (port)
#define HAL_GPIO_PIN(port, pin) (pin)

/// 以下为宏参数分解函数，无须更改。供底层驱动调用。
/* @brief 获取管脚的端口及编号分配 */
#define gpio_port(x) HAL_GPIO_PORT(x)
#define gpio_pin(x) HAL_GPIO_PIN(x)
#define gpio_pin_mask(x) (1 << HAL_GPIO_PIN(x))
/* @brief 设置管脚输入模式 */
#define gpio_set_input(x) HAL_GPIO_DIR_IN(x)
/* @brief 设置管脚输出模式 */
#define gpio_set_output(x) HAL_GPIO_DIR_OUT(x)
/* @brief 获取管脚输入电平 */
#define gpio_input_get(x) boolof(HAL_GPIO_IN_GET(x))
/* @brief 获取管脚输出电平 */
#define gpio_output_get(x) boolof(HAL_GPIO_OUT_GET(x))
/* @brief 管脚置高电平 */
#define gpio_out_H(x) HAL_GPIO_OUT_SET(x)
/* @brief 管脚置低电平 */
#define gpio_out_L(x) HAL_GPIO_OUT_RST(x)
/* @brief 管脚反转电平 */
#define gpio_out_rev(x) HAL_GPIO_OUT_REV(x)
/* @brief 管脚打开上拉 */
#define gpio_up_en(x) HAL_GPIO_PTUP_EN(x)
/* @brief 管脚关闭上拉 */
#define gpio_up_dis(x) HAL_GPIO_PTUP_DIS(x)
/* @brief 管脚打开下拉 */
#define gpio_pd_en(x) HAL_GPIO_PTPD_EN(x)
/* @brief 管脚关闭下拉 */
#define gpio_pd_dis(x) HAL_GPIO_PTPD_DIS(x)
/* @brief 管脚打开开漏 */
#define gpio_od_en(x) HAL_GPIO_PTOD_EN(x)
/* @brief 管脚关闭开漏 */
#define gpio_od_dis(x) HAL_GPIO_PTOD_DIS(x)
/// @brief 管脚打开高驱动能力
#define gpio_dr_en(x) HAL_GPIO_PTDR_EN(x)
/// @brief 管脚关闭高驱动能力
#define gpio_dr_dis(x) HAL_GPIO_PTDR_DIS(x)

/* @brief 管脚设置为模拟端口 */
#define gpio_ain(x) HAL_GPIO_AIN(x)
/* @brief 管脚设置为数字端口 */
#define gpio_ads(x) HAL_GPIO_ADS(x)

/// @brief 配置GPIO外部中断定义，需要时增加定义，不要删除
// #define IRQ_PORT_B04 true
// #define IRQ_PORT_B05 true
// #define IRQ_PORT_F00 true
// #define IRQ_PORT_F01 true

/* @brief 外部中断服务函数枚举 */
typedef enum
{
#if IRQ_PORT_B04
    TYPE_PB04 = 0,    // PB04
#endif
#if IRQ_PORT_B05
    TYPE_PB05,    // PB05
#endif
#if IRQ_PORT_F00
    TYPE_PF00,    // PF00
#endif
#if IRQ_PORT_F01
    TYPE_PF01,    // PF01
#endif
    TYPE_EXTI_NUM,
} GPIO_EXTI_TYPE;

typedef enum
{
    GPIO_OPEN,
    GPIO_CLOSE,
    GPIO_MONITOR,
} GPIO_INIT_TYPE_t;

/* Exported functions ------------------------------------------------------- */
struct hal_gpio_t
{
    /// @brief  串口GPIO配置
    void (*uart_init)(uint8_t com);

    /// @brief  GPIO配置
    void (*init)(void);

    /// @brief  GPIO低功耗下初始化
    void (*init_nopower)(void);

    /// @brief  输出脉冲模式设置
    void (*pulse_out_mode)(uint8_t mode);

    /// @brief IO口扫描，typ=0只扫描电源检测IO口，typ=1扫描所有IO口
    void (*monitor)(uint8_t typ);

    /// @brief 外部中断服务函数设置
    void (*exti_set)(uint8_t irq, void func(void));

    /// @brief lcd GPIO初始化
    void (*ext_lcd)(GPIO_INIT_TYPE_t typ);

    /// @brief FLASH GPIO初始化
    void (*data_flash)(GPIO_INIT_TYPE_t type);

    /// @brief eeprom GPIO初始化
    void (*ext_eeprom)(GPIO_INIT_TYPE_t type);

    /// @brief 计量芯片GPIO初始化
    void (*mic_init)(GPIO_INIT_TYPE_t type);

    /// @brief 远程通讯模组GPIO初始化
    void (*remote_module)(GPIO_INIT_TYPE_t type);

    /// @brief LED GPIO初始化
    void (*ext_led_init)(GPIO_INIT_TYPE_t type);

    /// @brief 继电器 GPIO初始化
    void (*ext_relay_init)(GPIO_INIT_TYPE_t type);
};
extern const struct hal_gpio_t hal_gpio;

#endif /* __HAL_GPIO_H */

/** @} */
/** @} */
// #ifdef __cplusplus
// }
// #endif
