/**
 ******************************************************************************
* @file    intvec_tab.h
* <AUTHOR> @date    2024
* @brief   
*
******************************************************************************
*
* @note
* Copyright (C) 2024  SheWei Electrics Co.,Ltd. All rights reserved.
*
*
******************************************************************************/

#ifndef __INTVEC_TAB_H__
#define __INTVEC_TAB_H__    

/// @brief 中断向量表枚举
/// @note  枚举值与中断向量表的索引值对应
// typedef enum 
// {
// 	INT_PORTA,
//     INT_PORTB,
//     INT_PORTC_E,
//     INT_PORTD_F,
//     INT_DMAC,
//     INT_TIM3,
//     INT_UART0_2,
//     INT_UART1_3,
//     INT_LPUART0,
//     INT_LPUART1,
//     INT_SPI0,
//     INT_SPI1,
//     INT_I2C0,
//     INT_I2C1,
//     INT_TIM0,
//     INT_TIM1,
//     INT_TIM2,
//     INT_LPTIM0_1,
//     INT_TIM4,
//     INT_TIM5,
//     INT_TIM6,
//     INT_PCA,
//     INT_WDT,
//     INT_RTC,
//     INT_ADC_DAC,
//     INT_PCNT,
//     INT_VC0,
//     INT_VC1_2,
//     INT_LVD,
//     INT_LCD,
//     INT_FLASH_RAM,
//     INT_CLKTRIM,

//     INT_NUM  ///< Total number of interrupt vectors
// } INT_TYPE;

typedef enum 
{
	INT_SYSCLKCAL,
    INT_CMP,
    INT_VCH,
    INT_RTC,
    INT_EMU,
    INT_MADC,
    INT_UART0,
    INT_UART1,
    INT_UART2,
    INT_UART3,
    INT_SPI0,
    INT_I2C,
    INT_ISO78160,
    INT_ISO78161,
    INT_TC0,
    INT_TC1,
    INT_UART4,
    INT_UART5,
    INT_WDT,
    INT_KBI,
    INT_LCD,
    INT_CP,
    INT_DMA,
    INT_NVM,
    INT_EXT0,
    INT_EXT1,
    INT_EXT2,
    INT_EXT3,
    INT_EXT4,
    INT_EXT5,
    INT_EXT6,
    INT_EXT7,
    INT_NUM  ///< Total number of interrupt vectors
} INT_TYPE;

//
extern void int_vector_set(int irq, void vec(void));



#endif /* __INTVEC_TAB_H__ */

