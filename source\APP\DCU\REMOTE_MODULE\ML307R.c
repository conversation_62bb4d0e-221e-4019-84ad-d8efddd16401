/**
 ******************************************************************************
 * @file    ML307R.c
 * <AUTHOR> @date    2025
 * @brief   中移ML307R 4G模块驱动
 *
 ******************************************************************************
 *
 * @note
 * Copyright (C) 2025  SheWei Electrics Co.,Ltd. All rights reserved.
 *
 *
 ******************************************************************************/
#include "pt.h"
#include "app.h"
#include "api.h"
#include "timeApp.h"
#include "module_para.h"
#include "remote_module.h"
#include "ringbuf.h"
#include "utils.h"
#include "dcu.h"

#define logd(...) DBG_PRINTF(P_REMOTE_MODULE, D, __VA_ARGS__)    // 模块日志打印宏
#define logm(...) DBG_PRINTF(P_REMOTE_MODULE, M, __VA_ARGS__)    // 通讯数据打印
#define logt(...) DBG_PRINTF(P_REMOTE_MODULE, T, __VA_ARGS__)    // 时间戳打印

#define atlog(...) DBG_PRINTF(P_REMOTE_MODULE_AT, D, __VA_ARGS__)    // AT命令日志打印宏
#define attm(...) DBG_PRINTF(P_REMOTE_MODULE_AT, M, __VA_ARGS__)     // AT命令数据打印
#define att(...) DBG_PRINTF(P_REMOTE_MODULE_AT, T, __VA_ARGS__)      // AT命令时间戳打印

#define R_BUAD_RATE BAUDE_115200BPS                 // 模块波特率
#define R_RXTX_SIZE (DCU_DATA_BUF_SIZE * 2 + 64)    // 模块接收发送缓存大小
#define R_RXTX_TIMEOUT 3500                         // 模块接收发送超时时间，单位ms
#define APDU_DATA_LEN_MIN 20                        // APDU数据最小长度,根据远程通讯协议最小长度定义

typedef enum module_response_enum
{
    RSP_NONE = 0,    // 无响应
    RSP_OK,          // 响应OK
    RSP_ERROR,       // 响应ERROR
    RSP_WAITING      // 等待响应
} R_RESPONSE_t;

// AT命令列表，为了减少搜索开支，注意顺序必须与r_at_cmd_list一致！！！
typedef enum r_at_cmd_e
{
    // 以下AT命令为入网流程使用的AT命令
    AT_AT = 0,      // 初始化AT命令
    AT_ATE0,        // 关闭回显AT命令
    AT_CPIN,        // 查询SIM卡状态
    AT_CSQ,         // 查询CSQ信号强度
    AT_SN,          // 查询SN号   AT+GSN=0
    AT_IEMI,        // 查询IMEI号 AT+GSN=1
    AT_CCID,        // 查询ICCID号
    AT_CEREG,       // 查询网络注册状态
    AT_SETCGATT,    // 手动附着
    AT_CGATT,       // 查询附着状态
    AT_CGPADDR,     // 查询IP地址
                    // 以上AT命令为入网流程使用的AT命令，添加注意更新入网流程指令数量

    // 以下AT命令为TCP/IP连接配置使用的AT命令
    AT_MIPCFG1,    // 设置TCP/IP连接超时时间
    AT_MIPCFG2,    // 设置TCP/IP数据格式
    AT_MIPTKA,     // 设置TCP/IP心跳参数
    AT_MIPOPEN,    // 打开TCP/IP连接

    AT_MIPSTATE,     // 查询TCP/IP连接状态
    AT_MIPCLOSE,     // 关闭TCP/IP连接
    AT_MIPSEND,      // 发送TCP/IP数据,
    AT_MIPRD,        // 接收TCP/IP数据, 流缓存模式读取缓存数据
    AT_MIPRD_LEN,    // 获取TCP/IP未读数据长度

    // 以下AT命令为运行中使用的AT命令
    AT_NTP,     // NTP时间同步命令
    AT_CCLK,    // 连接网络命令

    AT_RADIO,    // 获取基站信息
    AT_CELL,     // 获取小区信息
    AT_SBAND,    // 获取当前频段
    AT_MPING,    // ping命令
#if HTTP_UPGRADE_ENABLE
    AT_MHTTPDLFILE,    // HTTP下载文件命令

    AT_MFCLOSE,       // 关闭文件
    AT_MFOPEN,        // 打开文件
    AT_MFREAD,        // 读取文件
    AT_MFDELETE,      // 删除文件
    AT_MFSEEK,        // 读取文件指针位置
    AT_MFSEEK_SET,    // 设置文件指针位置
#endif
    AT_NULL
} R_AT_CMD_t;

#define AT_CONFG_NUM (AT_CGPADDR - AT_AT + 1)    // 入网流程AT命令数量

typedef enum r_module_err_typ_e
{
    ERR_NONE = 0,    // 无错误码
    ERR_CME          // CME错误
} R_MODULE_ERR_t;

typedef struct request_info_struct
{
    REQUEST_TYPE_t type;    // 请求类型
    uint8_t       *data;    // 请求数据指针
    uint16_t       len;     // 请求数据长度
} request_info_s;

typedef struct r_module_struct
{
    octstr_t      dcu;             // uart接收缓冲区
    uint16_t      dcusize;         // uart接收缓冲区大小
    uint16_t      frame_len;       // 帧长度
    MODULE_TASK_t task_state;      // 模块任务状态
    uint16_t      tcp_data_len;    // TCP数据接收长度
    uint8_t       err_code;
    bool          ret;    // 收到合法数据
} r_module_data_s;

typedef struct r_thread_struct
{
    struct pt main_pt;    // 主线程
    struct pt mpt;        // 子线程
    struct pt atpt;       // AT命令线程
    struct pt reqpt;      // 请求线程
    struct pt recvpt;     // 接收线程
} r_thread_s;

typedef struct r_at_cmd_struct
{
    R_AT_CMD_t cmd_typ;                                     // AT命令类型
    char      *str;                                         // AT命令请求字符串
    char      *match;                                       // AT指令响应匹配字段
    bool (*parse)(char *buf, char *match, uint16_t len);    // AT指令解析函数
    uint16_t timeout;                                       // AT指令超时时间，单位ms
} r_at_cmd_s;

typedef struct r_info_struct
{
    uint8_t  rssi;             // 信号强度
    uint8_t  ber;              // 误码率
    uint8_t  sn[20 + 1];       // SN号     第一位数据长度
    uint8_t  imei[15 + 1];     // IMEI号
    uint8_t  iccid[20 + 1];    // ICCID号
    uint8_t  sim;              // SIM卡状态
    uint8_t  tcp;              // TCP状态
    uint32_t cellid;           // 小区ID
    uint16_t pci;              // PCI
    uint8_t  sband;            // 当前频段
    uint8_t  rat;              // 连接网络类型
    uint16_t mcc;              // MCC
    uint16_t mnc;              // MNC
} r_module_info_s;

typedef struct r_module_http_s
{
    uint32_t file_tsize;     // 文件总大小
    uint32_t file_rsize;     // 文件已接收大小
    uint32_t file_offset;    // 文件偏移量

    uint32_t download_start;    // 下载开始位置
    uint32_t download_end;      // 下载结束位置

    uint32_t read_offset;    // 读取偏移量
    uint32_t read_len;       // 读取长度
} r_module_http_s;

static uint8_t          com_cache[R_RXTX_SIZE];    // 串口接收缓存区
static uint8_t          dcu_tx[R_RXTX_SIZE];       // 模块
static uint8_t          dcu_rx[R_RXTX_SIZE];       // 缓存
static r_module_data_s  r_data;
static r_module_state_s r_state;                          // 模块状态参数
static r_module_info_s  r_info;                           // 模块信息
static r_thread_s       r_pt;                             // 模块线程参数
static ringbuffer_s     r_ringbuf;                        // 模块接收环形缓冲区
static REQUEST_TYPE_t   r_request_type = REQUEST_NONE;    // 模块请求类型
static r_module_http_s  r_http;                           // 模块HTTP下载参数

static bool normal_parse(char *buf, char *match, uint16_t len);
static bool csq_parse(char *buf, char *match, uint16_t len);
static bool sn_parse(char *buf, char *match, uint16_t len);
static bool imei_parse(char *buf, char *match, uint16_t len);
static bool iccid_parse(char *buf, char *match, uint16_t len);
static bool cereg_parse(char *buf, char *match, uint16_t len);
static bool tcp0_open_parse(char *buf, char *match, uint16_t len);
static bool mipstate0_parse(char *buf, char *match, uint16_t len);
static bool mntp_parse(char *buf, char *match, uint16_t len);
static bool cclk_parse(char *buf, char *match, uint16_t len);
static bool radio_parse(char *buf, char *match, uint16_t len);
static bool cell_parse(char *buf, char *match, uint16_t len);
static bool sband_parse(char *buf, char *match, uint16_t len);
static bool mping_parse(char *buf, char *match, uint16_t len);
static bool tcp_rd_len_parse(char *buf, char *match, uint16_t len);

// AT命令列表，为了减少搜索开支，注意顺序必须与R_AT_CMD_t一致！！！
// 部分指令超时时间不要随意修改，注意等待时间timeout要大于AT指令超时时间。比如NTP时间同步命令，超时时间设置为3秒，timeout设置为3200ms
const r_at_cmd_s r_at_cmd_list[] =                                           // AT命令列表
    {{AT_AT, "AT\r\n", NULL, NULL, 200},                                     // AT命令
     {AT_ATE0, "ATE0\r\n", NULL, NULL, 200},                                 // 关闭回显AT命令
     {AT_CPIN, "AT+CPIN?\r\n", "+CPIN: READY", normal_parse, 200},           // 查询SIM卡状态
     {AT_CSQ, "AT+CSQ\r\n", "+CSQ: ", csq_parse, 200},                       // 查询CSQ信号强度
     {AT_SN, "AT+GSN=0\r\n", "\r\n", sn_parse, 200},                         // 查询SN号 20226M0001553M032254
     {AT_IEMI, "AT+GSN=1\r\n", "+GSN: ", imei_parse, 200},                   // 查询IMEI号  +GSN: 868381075370663
     {AT_CCID, "AT+ICCID\r\n", "+ICCID: ", iccid_parse, 200},                // 查询ICCID号 +ICCID: 898604B3192290011384
     {AT_CEREG, "AT+CEREG?\r\n", "+CEREG: ", cereg_parse, 200},              // 查询网络注册状态
     {AT_SETCGATT, "AT+CGATT=1\r\n", NULL, NULL, 200},                       // 手动附着
     {AT_CGATT, "AT+CGATT?\r\n", "+CGATT: 1", normal_parse, 200},            // 查询附着状态
     {AT_CGPADDR, "AT+CGPADDR\r\n", "+CGPADDR: 1,\"", normal_parse, 400},    // 查询PDP上下文IP地址，验证是否联网,不解析地址，只验证是否联网

     {AT_MIPCFG1, "AT+MIPCFG=\"timeout\",0,3\r\n", NULL, NULL, 200},       // 设置0通道TCP/IP连接超时时间，3-3秒
     {AT_MIPCFG2, "AT+MIPCFG=\"encoding\",0,1,1\r\n", NULL, NULL, 200},    // 设置0通道TCP/IP数据格式，收发:0-ASCII，1-HEX
     {AT_MIPTKA, "AT+MIPTKA=0,0,300,75,1\r\n", NULL, NULL, 200},           // 设置0通道TCP/IP心跳参数, 开启心跳300间隔，75秒重传间隔，重传次数1
     // 打开通道0 TCP连接,IP,PORT,超时时间10秒，0-普通模式，1-透传，2-流缓存模式： +MIPOPEN: 0,0
     // 打开TCP连接命令，%s-服务器IP地址，%u-端口号，3-超时时间，2-流缓存模式
     {AT_MIPOPEN, "AT+MIPOPEN=0,\"TCP\",\"%s\",%u,3,2\r\n", "+MIPOPEN: 0,", tcp0_open_parse, 3200},

     // 查询0通道TCP/IP连接状态 +MIPSTATE: 0,,,,"INITIAL"
     // +MIPSTATE: 0,"TCP","*************",2040,"CONNECTED"
     {AT_MIPSTATE, "AT+MIPSTATE=0\r\n", "+MIPSTATE: 0,\"TCP\",\"", mipstate0_parse, 200},    // 查询0通道TCP/IP连接状态
     {AT_MIPCLOSE, "AT+MIPCLOSE=0\r\n", "+MIPCLOSE: 0\r\n", normal_parse, 50},               // 关闭0通道TCP/IP连接  +MIPCLOSE: 0\r\n
     // AT+MIPSEND=0,11,"12345678900"
     // AT+MIPSEND=0,20,"683200320068C92032962C000270000001005016"
     {AT_MIPSEND, "AT+MIPSEND=0,%u,\"%s\"\r\n", "+MIPSEND: 0,", normal_parse, 200},    // 发送0通道TCP/IP数据, %x-hex格式， 0-通道，%d-数据长度
     // 接收0通道TCP/IP数据, 流缓存模式读取缓存数据，%d-数据长度
     //+MIPRD: 0,10,20,01234567890123456789 ,
     // 特殊指令，请求指令在函数中创建
     {AT_MIPRD, NULL, "+MIPRD: 0,", NULL, 300},                              // 需要做特殊解析。长数据
     {AT_MIPRD_LEN, "AT+MIPRD=0\r\n", "+MIPRD: ", tcp_rd_len_parse, 100},    // 获取0通道TCP/IP未读数据长度,

     // NTP时间同步命令,无端口，1-同步到本地，3-超时时间3秒 (不要随意改超时时间，注意小于 R_RXTX_TIMEOUT)
     // +MNTP: 0,"19/02/21,07:35:02+32
     {AT_NTP, "AT+MNTP=\"%s\",,1,3\r\n", "+MNTP: 0,", mntp_parse, 3200},    // NTP时间同步命令
     // +CCLK: 19/02/21,07:35:03+32 //本地时间同步成功
     {AT_CCLK, "AT+CCLK?\r\n", "+CCLK: \"", cclk_parse, 20},    // 获取CCLK时间

     //+MUESTATS: "radio",4,-620,-480,0,0,0,073B1007,255,270,38950,5,-30
     //"radio",<rat>,<rsrp>,<rssi>,<tx_power>,<tx_time>,<rx_time>,<last_cellid>,<last_ecl>,<last_sinr>,<last_earfcn>,<last_pci>,<rsrq>
     {AT_RADIO, "AT+MUESTATS=\"radio\"\r\n", "+MUESTATS: \"radio\",", radio_parse, 100},    // 获取基站信息,
     //+MUESTATS: "scell",4,460,00,38950,,5,-620,-30,-480,270,
     //"scell",<rat>,<mcc>,<mnc>,<earfcn>,<earfcn_offset>,<pci>,<rsrp>,<rsrq>,<rssi>,<sinr>[,<bandwidth>
     {AT_CELL, "AT+MUESTATS=\"cell\"\r\n", "+MUESTATS: \"scell\",", cell_parse, 100},    // 获取小区信息,
     //+MUESTATS: "sband",40
     //+MUESTATS: "sband",<sband>
     {AT_SBAND, "AT+MUESTATS=\"sband\"\r\n", "+MUESTATS: \"sband\",", sband_parse, 100},    // 获取当前频段,
     // ping命令，3秒超时，1次ping。固定网址（白宫官网国内外都可快速连接，用于测试模组是否能联网）
     {AT_MPING, "AT+MPING=\"www.whitehouse.gov\",3,1\r\n", "+MPING: ", mping_parse, 3200},
#if HTTP_UPGRADE_ENABLE
     // AT+MHTTPDLFILE="http://hardware-package.oss-cn-beijing.aliyuncs.com/1/DDDD1980(5-60A)-T4-app(V0.30-20250704).bin","/etc/m_app.bin",0,"bytes=0-30959",0
     // 特殊指令，请求指令在函数中创建
     {AT_MHTTPDLFILE, NULL, "+MHTTPDLFILE: ", NULL, 40000},    // HTTP下载文件命令

     {AT_MFCLOSE, "AT+MFCLOSE=0\r\n", NULL, NULL, 100},                                      // 关闭文件
     {AT_MFOPEN, "AT+MFOPEN=\"/etc/m_app.bin\",0\r\n", "+MFOPEN: 1,", normal_parse, 100},    // 打开文件
     // 特殊指令，请求指令在函数中创建
     {AT_MFREAD, NULL, "+MFREAD: 0,", NULL, 400},                                              // 读取文件
     {AT_MFDELETE, "AT+MFDELETE=\"/etc/m_app.bin\"\r\n", "+MFDELETE: ", normal_parse, 100},    // 删除文件
     {AT_MFSEEK, "AT+MFSEEK=1\r\n", NULL, NULL, 100},                                          // 读取文件指针位置
     {AT_MFSEEK_SET, "AT+MFSEEK=1,0,0\r\n", "+MFSEEK: 1,0", NULL, 100},                        // 设置文件指针位置
#endif
     // AT_NULL命令表示结束

     {AT_NULL, NULL, NULL, NULL, 0}};

/// @brief 清空接收缓冲区, 此函数不用修改
__INLINE void r_rbuf_clear(void)
{
    ringbuf_init(&r_ringbuf, dcu_rx, sizeof(dcu_rx));    // 初始化环形缓冲区
}

__INLINE void r_uart_open(void)
{
    hal_uart.open(COM_MODULE, UC_NONE, CHAR_8N1, R_BUAD_RATE, com_cache, sizeof(com_cache));    // 打开串口,中断方式
}

__INLINE void r_uart_open_polling(void)
{
    hal_uart.open(COM_MODULE, UC_NONE, CHAR_8N1, R_BUAD_RATE, NULL, NULL);    // 打开串口,polling方式
}

__INLINE void r_uart_send(const uint8_t *buf, uint16_t len)
{
    hal_uart.send(COM_MODULE, buf, len);    // 发送数据到串口
}

__INLINE void r_uart_print(char ch)
{
    hal_uart.print(COM_MODULE, ch);    // 打印字符串到串口
}

__INLINE void r_uart_close(void)
{
    hal_uart.close(COM_MODULE);    // 关闭串口
}

__INLINE uint16_t r_uart_recv(void *buf)
{
    return hal_uart.full_duplex_recv(COM_MODULE, buf);    // 从串口接收数据到缓存
}

/// @brief 此函数用于从串口接收数据到缓存区，并处理接收数据的长度。此函数不用修改
/// @note 该函数会检查接收缓冲区是否满，如果满了并且超时了，则清空接收缓冲区。 另外空间满时不从串口接收数据
__INLINE void r_recv_copy(void)
{
    static SwTimer_s tmr   = {0};    // 接收超时定时器
    uint16_t         space = 0;      // 缓冲区剩余空间

    space = ringbuf_space_left(&r_ringbuf);
    if(space == 0 && tmr.start == 0 && tmr.interval == 0)    // 如果缓冲区满且定时器未启动
    {
        hal_timer.interval(&tmr, R_RXTX_TIMEOUT);
    }
    else if(space == 0 && hal_timer.expired(&tmr))    // 空间满且定时器已到期，考虑数据无效
    {
        tmr.start = 0, tmr.interval = 0;
        logd("\r\nr_recv_copy: ringbuf full, clear it!\r\n");
        ringbuf_init(&r_ringbuf, dcu_rx, sizeof(dcu_rx));    // 清空接收缓冲区
        return;
    }

    if(space == 0) { return; }    // 如果缓冲区满，返回
    else
    {
        uint16_t len                  = 0;      // 接收数据长度
        uint8_t  buf[R_RXTX_SIZE + 1] = {0};    // 接收数据缓冲区
        tmr.start = 0, tmr.interval = 0;
        len = r_uart_recv(buf);    // 从串口接收数据到缓存
        if(len > 0)
        {
            ringbuf_write(&r_ringbuf, (const uint8_t *)buf, len);
            buf[len] = '\0';                                  // 确保接收缓冲区以'\0'结尾
            atlog("-->>uart data: %hu: %s\r\n", len, buf);    // 打印接收数据
        }
    }
}

bool r_uart_send_over_query(void)
{
    return hal_uart.send_over_query(COM_MODULE);    // 查询串口数据串是否发送完成(中断方式)
}

/// @brief pt线程初始化函数 此函数不用修改
/// @note  该函数用于初始化PT线程，设置PT的初始状态。此函数不用修改
/// @param
static void r_pt_init(void)
{
    PT_INIT(&r_pt.main_pt);    // 初始化主线程
    PT_INIT(&r_pt.mpt);        // 初始化子线程
    PT_INIT(&r_pt.atpt);       // 初始化AT命令线程
    PT_INIT(&r_pt.reqpt);      // 初始化请求线程
}

/// @brief 线程延时函数
/// @note 该函数用于在PT线程中进行延时操作，等待指定的毫秒数。此函数不用修改
/// @param pt
/// @param ms 毫秒
/// @return 线程状态
static char thread_delay(struct pt *pt, uint32_t ms)
{
    static SwTimer_s pt_tmr;    // 获取PT定时器

    PT_BEGIN(pt);
    hal_timer.interval(&pt_tmr, ms);                  // 设置定时器
    PT_WAIT_UNTIL(pt, hal_timer.expired(&pt_tmr));    // 等待定时器到期
    PT_END(pt);
}

/// @brief 设置错误码,根据ML307R模块的错误码进行处理。移植需核对手册修改
void error_code_set(uint16_t err_code)
{
    r_state.ce_error_code = err_code;    // 设置错误码
    if(err_code <= 63)
    {
        r_state.sim_state = err_code;
        if(err_code == 10) { r_state.no_sim = true; }
    }
    else if((err_code >= 550) && (err_code <= 582)) { r_state.tcp0_state = false; }
    else if((err_code >= 600) && (err_code <= 649)) { __NOP(); }
    else { return; }
}

/// 移植需核对手册修改
/// @brief 解析错误码
static uint16_t error_parse(char *buf, char *match, uint16_t len)
{
    char    *ptr;
    uint16_t err_code;
    if(len < 5) return 0xFFFF;
    if((ptr = strstr((const char *)buf, "ERROR")) == NULL) return 0xFFFF;    // 返回0xFFFF表示没有错误
    if((sscanf(ptr, "ERROR: %hu\r\n", &err_code) == 1))                      // 如果缓冲区中包含+CME ERROR字符串+CME  +CMS +CIS
    {
        memset(buf, 0, 6);                                // 清空缓冲区
        error_code_set(err_code);                         // 设置错误码
        logd("\r\ncat1 +CME ERROR: %d\r\n", err_code);    // 打印错误码
        return err_code;                                  // 返回错误字符串的长度
    }
    return 0xFFF0;    // AT命令格式错误或者其它错误
}

static bool ok_parse(char *buf, char *match, uint16_t len)
{
    if(len < 6) return false;                                           // 如果缓冲区长度小于OK响应长度，返回false
    if(strstr((const char *)buf, "OK\r\n") != NULL) { return true; }    // 如果缓冲区中包含OK字符串，返回true
    return false;
}

static bool normal_parse(char *buf, char *match, uint16_t len)
{
    bool ret = false;    // 默认返回false

    if(len < 10) return false;
    if(strstr(buf, match) != NULL) { return true; }

    return false;
}

//+CSQ: 99,99
static bool csq_parse(char *buf, char *match, uint16_t len)
{
    uint8_t rssi = 0, ber = 0;    // 信号强度和误码率
    if(len < 10) return false;
    if(sscanf(buf, "+CSQ: %hhu,%hhu\r\n", &rssi, &ber) == 2)    // 解析CSQ信号强度和误码率
    {
        r_info.rssi = rssi;                         // 设置信号强度
        r_info.ber  = ber;                          // 设置误码率
        if(rssi < 20 && rssi > 31) return false;    // 如果信号强度大于32，返回false表示解析失败
        return true;                                // 返回true表示解析成功
    }
    return false;    // 返回false表示解析失败
}

static bool sn_parse(char *buf, char *match, uint16_t len)
{
    char sn[20] = {0};    // SN号缓冲区

    if(len < 20) return false;                            // 如果缓冲区长度小于20，返回false
    if(sscanf(buf, "%20s\r\n", sn) != 1) return false;    // 解析SN号，如果解析失败返回false
    r_info.sn[0] = 20;
    memcpy(&r_info.sn[1], sn, sizeof(r_info.sn) - 1);    // 将SN号复制到模块信息结构体中

    return true;    // 返回true表示解析成功
}

static bool imei_parse(char *buf, char *match, uint16_t len)
{
    char imei[15] = {0};    // IMEI号缓冲区

    if(len < 15) return false;                                    // 如果缓冲区长度小于15，返回false
    if(sscanf(buf, "+GSN: %15s\r\n", imei) != 1) return false;    // 解析IMEI号，如果解析失败返回false
    r_info.imei[0] = 15;
    memcpy(&r_info.imei[1], imei, sizeof(r_info.imei) - 1);    // 将IMEI号复制到模块信息结构体中

    return true;    // 返回true表示解析成功
}

static bool iccid_parse(char *buf, char *match, uint16_t len)
{
    char iccid[20] = {0};    // ICCID号缓冲区

    if(len < 30) return false;                                       // 如果缓冲区长度小于20，返回false
    if(sscanf(buf, "+ICCID: %20s\r\n", iccid) != 1) return false;    // 解析ICCID号，如果解析失败返回false
    r_info.iccid[0] = 20;
    memcpy(&r_info.iccid[1], iccid, sizeof(r_info.iccid) - 1);    // 将ICCID号复制到模块信息结构体中

    return true;    // 返回true表示解析成功
}

static bool cereg_parse(char *buf, char *match, uint16_t len)
{
    uint8_t reg = 0, stat = 0;    // 网络注册状态和状态码
    if(len < 12) return false;
    if(sscanf(buf, "+CEREG: %hhu,%hhu\r\n", &reg, &stat) == 2)    // 解析网络注册状态和状态码
    {
        if(reg == 0 && (stat == 1 || stat == 5)) { return true; }    // 如果注册状态为1且状态码为1或5
    }
    return false;    // 返回false表示解析失败
}

static bool cgatt_parse(char *buf, char *match, uint16_t len)
{
    uint8_t stat = 0;    // 附着状态
    if(len < 10) return false;
    if(sscanf(buf, "+CGATT: %hhu\r\n", &stat) == 1)    // 解析附着状态
    {
        if(stat == 1) { return true; }    // 如果附着状态为1
    }
    return false;    // 返回false表示解析失败
}

static bool tcp0_open_parse(char *buf, char *match, uint16_t len)
{
    uint16_t state = 0;    // TCP连接状态
    uint8_t  chn   = 0;    // 通道号和状态

    if(len < 12) return false;
    r_state.tcp0_state      = false;                            // 初始化TCP0连接状态为false
    r_state.tcp0_open_parse = 0;                                // 初始化TCP0连接解析
    if(sscanf(buf, "+MIPOPEN: %hhu,%hu", &chn, &state) == 2)    // 解析TCP连接状态
    {
        if(chn == 0)
        {
            if(state == 0)
            {
                r_state.net_state  = true;    // 设置网络状态为true
                r_state.tcp0_state = true;
                return true;
            }
            else
            {
                r_state.tcp0_open_parse = state;    // 设置TCP0连接解析状态
            }
        }
    }
    return false;    // 返回false表示解析失败
}

static bool mipstate0_parse(char *buf, char *match, uint16_t len)
{
    char     ip[64] = {0};    // IP地址缓冲区
    uint16_t port   = 0;      // 端口号
    if(len < 30) return false;
    if(sscanf(buf, "+MIPSTATE: 0,\"TCP\",\"%s\",%hu,\"CONNECTED", ip, &port) == 3)    // 解析TCP连接状态
    {
        r_state.tcp0_state = true;                                       // 设置TCP0连接状态为true
        logd("\r\nTCP connection established: %s:%hu\r\n", ip, port);    // 打印TCP连接信息
        return true;                                                     // 返回true表示解析成功
    }
    return false;    // 返回false表示解析失败
}

static bool mntp_parse(char *buf, char *match, uint16_t len)
{
    clock_s clk  = {0};    // 时钟结构体
    int8_t  zone = 0;      // 时区
    if(len < 29) return false;
    if(sscanf(buf, "+MNTP: 0,\"%02hhu/%02hhu/%02hhu,%02hhu:%02hhu:%02hhu%hhd\"", &clk.year, &clk.month, &clk.day, &clk.hour, &clk.minute, &clk.second, &zone) ==
       7)    // 解析NTP时间
    {
        // mclock.zone_adjust(&clk, zone);
        // if(mclock.is_valid(&clk) == false) { return false; }    // 如果时钟无效，返回false
        // mclock.network_time_set(&clk);                          // 设置网络时间, 时间有1-2秒延迟，不建议使用
        return true;    // 返回true表示解析成功
    }
    return false;    // 返回false表示解析失败
}

static bool cclk_parse(char *buf, char *match, uint16_t len)
{
    clock_s clk  = {0};    // 时钟结构体
    int8_t  zone = 0;      // 时区
    if(len < 29) return false;
    if(sscanf(buf, "+CCLK: \"%02hhu/%02hhu/%02hhu,%02hhu:%02hhu:%02hhu%hhd\"", &clk.year, &clk.month, &clk.day, &clk.hour, &clk.minute, &clk.second, &zone) == 7)    // 解析CCLK时间
    {
        mclock.zone_adjust(&clk, zone);
        if(mclock.is_valid(&clk) == false) { return false; }    // 如果时钟无效，返回false
        mclock.network_time_set(&clk);                             // 设置同步时间
        return true;                                            // 返回true表示解析成功
    }
    return false;    // 返回false表示解析失败
}

static bool radio_parse(char *buf, char *match, uint16_t len)
{
    uint32_t cellid;    // 小区ID
    int16_t  tmp;
    uint16_t tmpu;
    uint8_t  rat;

    if(len < 20) return false;
    //+MUESTATS: "radio",4,-910,-420,-32768,0,0,FFFFFFFF,255,270,-1,65535,-30
    if(sscanf(buf, "+MUESTATS: \"radio\",%hhu,%hd,%hd,%hd,%hu,%hu,%x,%hu,%hu,%hd,%hu,%hd", &rat, &tmp, &tmp, &tmp, &tmpu, &tmpu, &cellid, &tmpu, &tmpu, &tmp, &tmpu, &tmp) ==
       12)    // 解析基站信息
    {
        r_info.rat    = rat;       // 设置连接网络类型
        r_info.cellid = cellid;    // 设置小区ID
        return true;               // 返回true表示解析成功
    }
    return false;    // 返回false表示解析失败
}

static bool cell_parse(char *buf, char *match, uint16_t len)
{
    int16_t  tmp;
    uint16_t mcc, pci, tmpu;    // MCC和MNC
    uint8_t  rat, mnc;
    if(len < 20) return false;
    //+MUESTATS: "scell",4,460,00,1300,,249,-910,-90,-460,110,
    if(sscanf(buf, "+MUESTATS: \"scell\",%hhu,%hu,%hhu,%hu,,%hu,%hd,%hd,%hd,%hu", &rat, &mcc, &mnc, &tmpu, &pci, &tmp, &tmp, &tmp, &tmpu) == 9)    // 解析小区信息
    {
        r_info.mcc = mcc;    // 设置MCC
        r_info.mnc = mnc;    // 设置MNC
        r_info.pci = pci;    // 设置PCI
        return true;         // 返回true表示解析成功
    }
    return false;    // 返回false表示解析失败
}

static bool sband_parse(char *buf, char *match, uint16_t len)
{
    uint8_t sband = 0;    // 频段
    if(len < 22) return false;
    if(sscanf(buf, "+MUESTATS: \"sband\",%02hhu\r\n", &sband) == 1)    // 解析频段
    {
        r_info.sband = sband;    // 设置当前频段
        return true;             // 返回true表示解析成功
    }
    return false;    // 返回false表示解析失败
}

static bool mping_parse(char *buf, char *match, uint16_t len)
{
    uint16_t tmp;
    // +MPING: 4
    // +MPING: 0,"***********",16,55,49
    // 0-通道号，3-发送次数，1-成功次数，后面6个参数为延时和丢包率等信息
    if(len < 10) return false;
    r_state.net_state = false;
    if(sscanf(buf, "+MPING: %hu,", &tmp) == 1)    // 解析MPING响应
    {
        if(tmp == 0)
        {
            r_state.net_state = true;
            return true;
        }    // 如果MPING响应为0，表示成功
    }
    return false;    // 返回false表示解析失败
}

//+MIPRD: 0,26
static bool tcp_rd_len_parse(char *buf, char *match, uint16_t len)
{
    uint16_t rlen;    // 接收数据长度
    uint8_t  chn;     // 通道号
    if(len < 12) return false;
    if(sscanf(buf, "+MIPRD: %hhu,%hu", &chn, &rlen) == 2)    // 解析接收数据长度
    {
        if(chn == 0) { r_data.tcp_data_len = rlen; }
        return true;    // 返回true表示解析成功
    }
    return false;    // 返回false表示解析失败
}

/// @brief 创建通用AT命令
/// @note 该函数用于创建通用AT命令，根据不同的AT命令类型生成对应的AT命令字符串。不同模组此函数一般不需要修改
/// @param buf AT命令缓冲区指针
/// @param cmd AT命令结构体指针
/// @return AT命令长度
static uint16_t at_cmd_creat(uint8_t *buf, r_at_cmd_s *cmd)
{
    uint16_t len = 0;

    if(cmd->str != NULL)    // 如果AT命令字符串不为空
    {
        if(cmd->cmd_typ == AT_MIPOPEN)    // 如果是打开TCP连接的AT命令
        {
            // 格式化AT命令字符串，使用IP地址和端口号
            char    ip[MODULE_IP_LEN + 1];
            uint8_t dlen;

            dlen     = module_para.para_get(ip, MODULE_TCP);    // 获取模块IP地址
            ip[dlen] = '\0';                                    // 确保IP地址字符串以'\0'结尾
            len      = sprintf((char *)buf, cmd->str, ip, module_para.para_get(ip, MODULE_TCP_PORT));
        }
        else if(cmd->cmd_typ == AT_NTP)    // 如果是NTP时间同步的AT命令
        {
            // 格式化AT命令字符串，使用NTP服务器地址
            char    ip[MODULE_IP_LEN + 1];
            uint8_t dlen;

            dlen     = module_para.para_get(ip, MODULE_NTP_SERVER);    // 获取NTP服务器IP地址
            ip[dlen] = '\0';                                           // 确保IP地址字符串以
            len      = sprintf((char *)buf, cmd->str, ip);
        }
        // else if(cmd->cmd_typ == AT_MHTTPDLFILE)
        // {
        //     // 格式化AT命令字符串，使用HTTP下载文件的URL和范围
        //     uint16_t dlen;
        //     char     url[MODULE_HTTP_URL_LEN + 1];    // URL缓冲区

        //     dlen      = module_para.para_get(url, MODULE_HTTP_URL);    // 获取HTTP下载文件的URL
        //     url[dlen] = '\0';                                          // 确保URL字符串以'\0'结尾
        //     len       = sprintf((char *)buf, cmd->str, url, r_http.download_start, r_http.download_end);
        // }
        // else if(cmd->cmd_typ == AT_MFREAD)
        // {
        //     // 格式化AT命令字符串，使用文件读取长度
        //     len = sprintf((char *)buf, cmd->str, r_http.read_len);
        // }
        else    // 其他AT命令
        {
            len = strlen(cmd->str);        // 格式化AT命令字符串
            memcpy(buf, cmd->str, len);    // 将AT命令字符串复制到缓冲区
        }
    }
    return len;    // 返回AT命令长度
}

/// @brief 创建TCP/IP接收数据AT命令，移植可能要改
/// @note 该函数用于创建TCP/IP接收数据的AT命令，根据接收数据长度生成对应的AT命令字符串。
/// @param buf AT命令缓冲区指针
/// @param len 接收数据长度
/// @return AT命令长度
static uint16_t at_cmd_miprd_creat(uint8_t *buf, uint16_t len)
{
    return sprintf((char *)buf, "AT+MIPRD=0,%hu\r\n", len);    // 返回AT命令长度
}

/// @brief 创建HTTP下载文件AT命令
/// @note 该函数用于创建HTTP下载文件的AT命令，根据下载文件的起始和结束位置生成对应的AT命令字符串。
/// @param buf AT命令缓冲区指针
/// @param dstart 下载文件的起始位置
/// @param dend 下载文件的结束位置
/// @return AT命令长度
/// @note 该函数可能需要根据不同模组的AT命令格式进行调整
static uint16_t at_cmd_httpdlfile_creat(uint8_t *buf, uint32_t dstart, uint32_t dend)
{
    // 格式化AT命令字符串，使用HTTP下载文件的URL和范围
    uint16_t len;
    uint16_t dlen;
    char     url[MODULE_HTTP_URL_LEN + 1];    // URL缓冲区

    dlen      = module_para.para_get(url, MODULE_HTTP_URL);    // 获取HTTP下载文件的URL
    url[dlen] = '\0';                                          // 确保URL字符串以'\0'结尾
    len       = sprintf((char *)buf, "AT+MHTTPDLFILE=\"%s\",\"/etc/m_app.bin\",0,\"bytes=%u-%u\",0\r\n", url, dstart, dend);
    return len;    // 返回AT命令长度
}

/// @brief 创建读取文件AT命令
/// @note 该函数用于创建读取文件的AT命令，根据读取数据长度生成对应的AT命令字符串。
/// @param buf AT命令缓冲区指针
/// @param len 读取数据长度
/// @return AT命令长度
/// @note 该函数可能需要根据不同模组的AT命令格式进行调整
static uint16_t at_cmd_mfread_creat(uint8_t *buf, uint16_t len)
{
    return sprintf((char *)buf, "AT+MFREAD=0,%hu\r\n", len);    // 返回AT命令长度
}

/// @brief  处理TCP/IP URC上报信息
/// @note   该函数用于处理TCP/IP URC上报信息，根据上报信息更新TCP连接状态。此函数根据不同模组类型可能需要进行不同的解析处理。
/// @param  buf 上报信息缓冲区指针
/// @return 无返回值
static void tcp_urc_report(const uint8_t *buf)
{
    char *ptr = (char *)buf;

    if((ptr = strstr((const char *)buf, "+MIPURC: \"")) != NULL && strstr(ptr + 2, "\r\n") != NULL)
    {
        uint16_t rlen, tlen;
        uint8_t  chn;      // 通道号
        uint8_t  ret;      // 解析结果
        uint8_t  state;    // 状态

        ret = sscanf(ptr, "+MIPURC: \"disconn\",%hhu,%hhu", &chn, &state);    // tcp断开连接上报
        if(ret == 2 && chn == 0)                                              // 如果解析成功且通道号为0
        {
            r_state.tcp0_state = false;                                     // 设置TCP0连接状态为false
            logd("\r\nTCP0 channel disconnected, state: %d\r\n", state);    // 打印断开连接状态
        }

        ret = sscanf(ptr, "+MIPURC: \"rtcp\",%hhu,%hu,%hu,", &chn, &rlen, &tlen);    // TCP接收缓存数据上报
        if(ret == 3 && chn == 0)                                                     // 如果解析成功且通道号为0
        {
            logd("\r\nTCP0 channel data received, recv_length: %hu, total_length: %hu\r\n", rlen, tlen);    // 打印接收数据长度和发送数据长度
            r_data.tcp_data_len = rlen;                                                                     // 设置接收数据长度
        }
    }
}

/// @brief 获取AT命令响应消息, 如果AT指令消息分割符不变，移植时不用修改
/// @note 该函数用于获取AT命令响应消息，ML307R 消息分割符为 "\r\n"。 格式为 "\r\nxxx\r\n"。
/// @param buf 接收缓冲区指针 输出内容跳过起始"\r\n"格式为 xxx\r\n
/// @param cstart 消息起始分割符
/// @param cend 消息结束分割符
/// @return 返回消息长度
#define MIN_MSG_LEN 6    // 最小消息长度
static uint16_t get_message(uint8_t *buf, const char *cstart, const char *cend)
{
    int16    start;
    uint16_t len = 0;    // 接收数据长度

    if((len = ringbuf_data_len(&r_ringbuf)) < MIN_MSG_LEN) return 0;    // 如果接收数据长度小于5，返回0
    if(len > R_RXTX_SIZE) len = R_RXTX_SIZE;
    if((start = ringbuf_search(&r_ringbuf, cstart, 0) < 0)) { return 0; }    // 查找起始位置
    start += strlen(cstart);                                                 // 起始位置加上分割字段长度
    if(start > len) { return 0; }                                            // 如果起始位置加结尾分割字段大于接收数据长度，返回0
    // 再加1个字符开始获取消息长度，\r\nC\r\n，保证内容至少1个字符，\r\n\r\n 中间没有字符可能是两条消息
    if((len = ringbuf_get_until(&r_ringbuf, cend, start + 1, buf)) == 0) { return 0; }    // 获取消息直到分割符，返回消息长度

    buf[len] = '\0';                          // 确保缓冲区以'\0'结尾
    memcpy(buf, buf + start, len - start);    // 将消息复制到缓冲区，去除起始"\r\n"及前面无效数据部分即 xxx\r\n
    return len;                               // 返回消息长度
}

//+MFREAD: 1,512,512,512,
/// @brief 获取文件数据
/// @note 该函数用于获取文件数据，根据起始分割符和接收数据长度从环形缓冲区中读取数据。
/// @param buf 接收缓冲区指针
/// @param cstart 消息起始分割符
/// @param rlen 接收文件包长度
/// @return 返回消息长度
/// @note 该函数可能需要根据不同模组的AT命令格式进行调整
static uint16_t get_file_data(uint8_t *buf, const char *cstart, uint16_t rlen)
{
    int16    start;
    int16    offset;
    uint16_t len = 0;    // 接收数据长度

    if((len = ringbuf_data_len(&r_ringbuf)) < rlen) return 0;    // 如果接收数据长度小于5，返回0
    if(len > R_RXTX_SIZE) len = R_RXTX_SIZE;
    if((start = ringbuf_search(&r_ringbuf, cstart, 0) < 0)) { return 0; }    // 查找起始位置
    offset = start + strlen(cstart);                                         // 起始位置加上分割字段长度
    if(offset > len || ((len - offset) < rlen)) { return 0; }
    for(uint16_t i = 0; i < 3; i++)    // 查找三个','
    {
        if((offset = ringbuf_search(&r_ringbuf, ",", offset) < 0)) { return 0; }    // 查找起始位置
        offset += 1;                                                                // 起始位置加上分割字段长度
        if(offset > len || ((len - offset) < rlen)) { return 0; }
    }
    if(offset > len || ((len - offset) < rlen)) { return 0; }    // 数据域少于要读取的长度返回。
    // 再加1个字符开始获取消息长度，\r\nC\r\n，保证内容至少1个字符，\r\n\r\n 中间没有字符可能是两条消息
    if((len = ringbuf_read_from(&r_ringbuf, start, offset - start + rlen, buf)) == 0) { return 0; }

    buf[len] = '\0';
    return len;    // 返回消息长度
}

/// @brief 处理模块响应数据，不可用于数据据读取及数据发送解析。不同模块可能要改
/// @note 该函数用于处理接收到的模块响应数据，根据实际需要解析数据。
/// @param buf 接收缓冲区指针
/// @param
/// @param len 数据长度
#pragma optimize = none
static R_RESPONSE_t at_response(r_at_cmd_s *cmd)
{
    static bool  ret_ok    = false;          // 响应状态
    static bool  ret_parse = false;          // 解析状态
    static bool  ret_error = false;          // 错误状态
    R_RESPONSE_t ret       = RSP_WAITING;    // 响应结果
    uint8_t      i;

    // r_recv_copy();    // 从串口接收数据到环形缓冲区
    i = 0;
    while(i++ < 5)    // 每次最多处理4条消息
    {
        uint16_t msglen;
        uint8_t  buf[R_RXTX_SIZE + 1];

        r_recv_copy();                  // 从串口接收数据到环形缓冲区
        memset(buf, 0, sizeof(buf));    // 清空接收数据缓冲区
        if((msglen = get_message(buf, "\r\n", "\r\n")) == 0) { break; }
        att();
        atlog("get_message: msglen: %d, msg: %s\r\n", msglen, buf);    // 打印消息长度、起始位置和接收数据长度
        if(msglen >= (MIN_MSG_LEN - 2))
        {
            if(error_parse((char *)buf, NULL, msglen) != 0xFFFF) { ret_error = true; }
            else if(ok_parse((char *)buf, NULL, msglen)) { ret_ok = true; }
            else if(cmd->parse != NULL && cmd->parse((char *)buf, cmd->match, msglen)) { ret_parse = true; }
            else { tcp_urc_report(buf); }
        }
    }

    if(cmd->parse != NULL && ret_parse && ret_ok) { ret = RSP_OK; }
    else if(ret_ok && cmd->parse == NULL) { ret = RSP_OK; }    // 如果解析状态为true，返回RSP_OK
    else if(ret_error || i >= 5) { ret = RSP_ERROR; }          // 如果错误状态为true或处理超过4条消息任然没有解析到有效数据,返回RSP_ERROR

    if(ret != RSP_WAITING)
    {
        // 如果响应状态不是等待状态，表示已经处理完毕，重置所有响应状态
        ret_ok    = false;
        ret_parse = false;
        ret_error = false;
        logt();
        logd("at_response: [cmd]: %d,--> [ret]: %d, [ok]: %d, [parse]: %d, [error]: %d \r\n", cmd->cmd_typ, ret, ret_ok, ret_parse, ret_error);    //
    }

    return ret;
}

/// @brief AT命令请求函数
/// @note 该函数用于发送AT命令请求模块，并处理响应。通用函数，不同模组无需修改。
/// @param pt PT线程指针
/// @param cmd AT命令类型
/// @return PT线程状态
static char at_command(struct pt *pt, R_AT_CMD_t cmd)
{
    static SwTimer_s tim;
    r_at_cmd_s      *cmd_ptr = (r_at_cmd_s *)&r_at_cmd_list[cmd];    // AT命令结构体指针
    uint16_t         len     = 0;
    R_RESPONSE_t     rsp     = RSP_WAITING;

    PT_BEGIN(pt);

    r_data.ret = false;
    if(cmd != cmd_ptr->cmd_typ)
    {
        logd("at_command: cmd not match, cmd: %d, ptr: %d\r\n", cmd, cmd_ptr->cmd_typ);    // 指令不匹配，结构体与枚举定义顺序不一致！！
        PT_EXIT(pt);                                                                       // 退出PT线程
    }
    len = at_cmd_creat(dcu_tx, cmd_ptr);    // 创建AT命令
    if(len == 0) PT_EXIT(pt);               // 如果AT命令长度为0，退出
#if P_REMOTE_MODULE
    if(len < sizeof(dcu_tx)) { dcu_tx[len] = '\0'; }    // 确保AT命令以'\0'结尾
#endif
    logt();
    logd("Sent AT command: %s\r\n", dcu_tx);    // 打印发送的AT命令
    r_uart_send(dcu_tx, len);                   // 发送AT命令到模块

    // 等待模块响应
    hal_timer.interval(&tim, 10);
    PT_WAIT_UNTIL(pt, hal_timer.expired(&tim));

    hal_timer.interval(&tim, cmd_ptr->timeout);    // 设置超时时间

    PT_WAIT_UNTIL(pt, (rsp = at_response(cmd_ptr)) != RSP_WAITING || hal_timer.expired(&tim));    // 等待接收缓冲区有数据
    if(rsp == RSP_OK) { r_data.ret = true; }                                                      // 设置返回状态为true，表示成功

    PT_END(pt);
}

/// @brief 模块请求函数
/// @note 该函数用于发送AT命令请求模块，并处理响应。通用函数，不同模组无需修改。
/// @param pt        // PT线程指针
/// @param trytimes  // 重试次数
/// @param delay     // 重试延时，单位为毫秒
/// @param cmd       // AT命令类型
/// @return          //
static char module_request(struct pt *pt, uint8_t trytimes, uint16_t delay, R_AT_CMD_t cmd)
{
    static uint8_t repeat = 0;    // 重试次数

    PT_BEGIN(pt);
    do {
        PT_SPAWN(pt, &r_pt.atpt, at_command(&r_pt.atpt, cmd));    // 生成AT指令请求线程
        if(!r_data.ret)                                           // 如果请求失败
        {
            logd("\r\nRequest %d failed, repeat %d\r\n", cmd, repeat);
            if(trytimes > 1)
            {
                PT_WAIT_THREAD(pt, thread_delay(&r_pt.atpt, delay));    // 超时后延时重试
            }
        }
    } while(++repeat < trytimes && !r_data.ret);    // 重试直到达到最大次数或成功

    repeat          = 0;                       // 重置重试次数
    r_data.err_code = r_data.ret ? 0 : cmd;    // 设置错误码
    PT_END(pt);                                /// AT命令请求结束
}

/// @brief 模块配置线程
/// @note 该函数用于配置模块,失败需要重试。不同模组需要改复位信号和开机信号，其它不用改
static PT_THREAD(r_module_configure_thread(struct pt *pt))
{
    static uint8_t cmd = 0;    // AT命令索引
    PT_BEGIN(pt);

    // 模块配置线程，执行AT命令配置
    logd("\r\nModule configuration thread started\r\n");

    logt();
    logd("power off\r\n");
    bsp.remote_module(1, 1);                               // 关闭复位信号
    bsp.remote_module(2, 1);                               // POWERKEY拉高，模块开机状态
    bsp.remote_module(0, 0);                               // 关闭电源
    PT_WAIT_THREAD(pt, thread_delay(&r_pt.atpt, 5000));    // 延迟5秒供电
    bsp.remote_module(0, 1);                               // 打开电源

    logt();
    logd("power on\r\n");
    // 等待300ms，确保模块电源稳定，手册要求100ms
    PT_WAIT_THREAD(pt, thread_delay(&r_pt.atpt, 150));    // 延时5秒，等待电源稳定

    logd("Module reset\r\n");
    // 复位信号307R手册300ms
    bsp.remote_module(1, 0);    // 使能复位
    PT_WAIT_THREAD(pt, thread_delay(&r_pt.atpt, 350));
    bsp.remote_module(1, 1);    // 关闭复位信号

    // 等待模块复位完成
    PT_WAIT_THREAD(pt, thread_delay(&r_pt.atpt, 10));    // 等待100ms，确保模块复位完成

    logd("Module pwrkey on-off\r\n");
    // 开机信号2100ms，307R手册关机状态拉低2-3.5s->开机,开机状态拉低3.5-4s->关机
    bsp.remote_module(2, 0);
    PT_WAIT_THREAD(pt, thread_delay(&r_pt.atpt, 2400));
    bsp.remote_module(2, 1);

    logd("wait for module ready\r\n");
    // 等待开机完成及自动注册
    PT_WAIT_THREAD(pt, thread_delay(&r_pt.atpt, 5000));

    logd("Module ready, start AT commands\r\n");
    for(cmd = (uint8_t)AT_AT; cmd < (uint8_t)AT_CONFG_NUM; cmd++)    // 遍历所有AT命令
    {
        PT_WAIT_THREAD(pt, module_request(&r_pt.reqpt, 3, 1500, (R_AT_CMD_t)cmd));    // 发送AT命令请求
        if(r_data.err_code) { break; }                                                // 如果发生错误，跳出循环
    }

    if(!r_data.err_code)
    {
        r_data.task_state = M_TASK_TCP;    // 如果没有错误，转到TCP连接任务
        r_data.ret        = false;
        PT_EXIT(pt);    /// 完成初始化，退出配置线程
    }
    else
        r_data.task_state = M_TASK_INIT;

    logd(">> err_InitAt code = %d << \r\n", r_data.err_code);
    PT_WAIT_THREAD(pt, thread_delay(&r_pt.atpt, 2000));    // 出错，延时2s退出配置线程

    PT_END(pt);    /// 初始化失败，结束配置线程
}

/// @brief 模块TCP连接线程
/// @note 该函数用于建立TCP连接，失败需重试，移植不用修改
static PT_THREAD(r_module_tcp_connection(struct pt *pt))
{
    static uint8_t cmd = 0;    // AT命令索引
    PT_BEGIN(pt);

    // tcp连接前先主动关闭TCP连接，可以不等待应答并且忽略错误应答。
    PT_WAIT_THREAD(pt, module_request(&r_pt.reqpt, 1, 200, AT_MIPCLOSE));

    for(cmd = (uint8_t)AT_MIPCFG1; cmd <= (uint8_t)AT_MIPOPEN; cmd++)    // 遍历所有AT命令
    {
        PT_WAIT_THREAD(pt, module_request(&r_pt.reqpt, 2, 500, (R_AT_CMD_t)cmd));    // 发送AT命令请求
        if(r_data.err_code) { break; }                                               // 如果发生错误，跳出循环
    }

    if(!r_data.err_code)
    {
        r_data.task_state = M_TASK_NORMAL;
        r_data.ret        = false;
        PT_EXIT(pt);    /// 完成初始化，退出配置线程
    }
    else
    {
        // 如果tcp连接失败，先获取基站信息，然后尝试ping网络
        PT_WAIT_THREAD(pt, module_request(&r_pt.reqpt, 2, 500, AT_CSQ));
        PT_WAIT_THREAD(pt, module_request(&r_pt.reqpt, 2, 500, AT_RADIO));
        PT_WAIT_THREAD(pt, module_request(&r_pt.reqpt, 2, 500, AT_CELL));
        PT_WAIT_THREAD(pt, module_request(&r_pt.reqpt, 2, 500, AT_SBAND));
        PT_WAIT_THREAD(pt, module_request(&r_pt.reqpt, 2, 500, AT_MPING));

        if(r_data.ret)
        {
            // 如果ping网络成功
            r_data.err_code   = 0;
            r_data.ret        = false;
            r_data.task_state = M_TASK_TCP;    // 重试tcp连接
            logd("Network OK, TCP connection retrying...\r\n");
            PT_WAIT_THREAD(pt, thread_delay(&r_pt.atpt, 60000));    // 延时60s重试TCP连接
            PT_EXIT(pt);
        }
        else    // 如果ping网络失败
        {
            r_data.task_state = M_TASK_INIT;    // 重置任务状态为初始化
            logd("\r\nTCP connection failed, network unreachable or module error");
        }
    }

    logd("\r\n>> tcp connection error = %d <<\r\n", r_data.err_code);
    PT_WAIT_THREAD(pt, thread_delay(&r_pt.atpt, 5000));    // 出错，延时5s退出配置线程

    PT_END(pt);    /// 初始化失败，结束配置线程
}

/// @brief 获取模块信息线程
/// @note 该函数用于获取模块信息，包括信号强度、网络注册状态，移植可不改
static PT_THREAD(r_module_info_get(struct pt *pt))
{
    PT_BEGIN(pt);

    // 获取模块信息线程
    logd("\r\nModule info get thread started !\r\n");
    // 获取模块信息
    PT_WAIT_THREAD(pt, module_request(&r_pt.reqpt, 2, 500, AT_CSQ));      // 获取信号强度
    PT_WAIT_THREAD(pt, module_request(&r_pt.reqpt, 2, 500, AT_RADIO));    // 获取网络注册状态
    PT_WAIT_THREAD(pt, module_request(&r_pt.reqpt, 2, 500, AT_CELL));     // 获取附着状态
    PT_WAIT_THREAD(pt, module_request(&r_pt.reqpt, 2, 500, AT_SBAND));    // 获取SN号

    DBG_PRINTF(P_DCU, D, "\r\nModule info: CSQ: %hhu, RAT: %hhu, MCC: %hu, MNC: %hu, PCI: %hu, CellID: 0x%08X, SBand: %hhu\r\n", r_info.rssi, r_info.rat, r_info.mcc, r_info.mnc, r_info.pci, r_info.cellid,
               r_info.sband);
    PT_END(pt);    /// 模块信息获取结束，退出线程
}

/// @brief 同步网络时间线程
/// @note 该函数用于同步网络时间，移植可不改
static PT_THREAD(r_module_network_time_sync(struct pt *pt))
{ 
    PT_BEGIN(pt);

    // 同步网络时间
    PT_WAIT_THREAD(pt, module_request(&r_pt.reqpt, 1, 500, AT_NTP));    // 获取NTP时间
    if(r_data.ret)                                                      // ntp时间同步成功再获取cclk，确保cclk的准确性
    {
        PT_WAIT_THREAD(pt, module_request(&r_pt.reqpt, 2, 100, AT_CCLK));    // 同步网络时间
    }
    PT_END(pt);    /// 网络时间同步结束，退出线程
}

/// @brief 处理模块响应数据，正常运行时使用
/// @note 该函数用于处理模块响应数据，正常运行时使用,主要处理模块主动信息。移植无需修改
static void normal_run_message(void)
{
    uint8_t i = 0;
    uint8_t buf[R_RXTX_SIZE];

    r_recv_copy();    // 从串口接收数据到环形缓冲区
    while(++i < 5)
    {
        uint16_t msglen;

        memset(buf, 0, sizeof(buf));    // 清空接收数据缓冲区
        if((msglen = get_message(buf, "\r\n", "\r\n")) == 0) { break; }
        logt();
        logd("get_message: msglen: %d, msg: %s\r\n", msglen, buf);    // 打印消息长度、起始位置和接收数据长度
        if(msglen >= (MIN_MSG_LEN - 2))
        {
            error_parse((char *)buf, NULL, msglen);
            ok_parse((char *)buf, NULL, msglen);
            tcp_urc_report(buf);
        }
    }
}

static bool mpird_parse(char *buf, r_module_data_s *out, uint16_t len)
{
    char    *ptr = buf;     // 指针
    uint16_t rlen, tlen;    // 接收数据长度和总长度
    uint8_t  chn;           // 通道号

    if(len < 17) return false;
    if(sscanf(buf, "+MIPRD: %hhu,%hu,%hu,", &chn, &rlen, &tlen) == 3)    // 解析接收数据长度
    {
        if(chn == 0)
        {
            r_data.tcp_data_len = rlen;
            if(tlen)
            {
                if(tlen + out->dcu.len > out->dcusize) { tlen = out->dcusize - out->dcu.len; }    // 如果接收数据长度加上当前长度大于最大长度，设置为最大长度
                // 数据在第三个‘,’后面
                if((ptr = strstr(ptr, ",")) == NULL) return 0;
                ptr++;
                if((ptr = strstr(ptr, ",")) == NULL) return 0;
                ptr++;
                if((ptr = strstr(ptr, ",")) == NULL) return 0;
                ptr++;

                string_to_hexarray(out->dcu.str + out->dcu.len, (const char *)(ptr), tlen * 2);    // 将接收数据转换为十六进制数组
                out->dcu.len += tlen;                                                              // 更新接收数据长度
                return tlen;
            }
            return 0;    // 返回true表示解析成功
        }
    }
    return false;    // 返回false表示解析失败
}

//+MHTTPDLFILE: 32769,32769,100,87252 (已下载数据长度，本次总共需要下载长度，下载进度，下载文件总长度)
/// @brief 解析HTTP下载文件响应
/// @note 该函数用于解析HTTP下载文件响应，根据下载进度和文件长度进行解析。
/// @param buf HTTP下载文件响应缓冲区指针
/// @param read_len 本次需要下载的长度
/// @param len 响应数据长度
/// @return 返回true表示解析成功，false表示解析失败
/// @note 该函数可能需要根据不同模组的AT命令格式进行调整
static bool http_dlfile_parse(char *buf, uint32_t read_len, uint16_t len)
{
    uint32_t rlen, tlen, file_len;
    uint8_t  per;

    if(len < 20) return false;
    if(sscanf(buf, "+MHTTPDLFILE: %u,%u,%hhu,%u", &rlen, &tlen, &per, &file_len) == 4)
    {
        // 解析HTTP下载文件响应
        if(per != 100 || rlen != read_len || rlen != tlen) return false;    // 如果下载进度不是100%，表示下载未完成
        if(r_http.file_tsize == 0) { r_http.file_tsize = file_len; }
        else if(r_http.file_tsize != file_len)
        {
            logd("HTTP file size mismatch: expected %u, got %u\r\n", r_http.file_tsize, file_len);
            return false;    // 如果文件大小不匹配，返回false表示解析失败
        }

        return true;
    }
    return false;    // 返回false表示解析失败
}

/// @brief 解析读取文件响应
/// @note 该函数用于解析读取文件响应，根据读取数据长度和文件内容进行解析。
/// @param buf 读取文件响应缓冲区指针
static bool read_file_parse(char *buf, uint8_t *file, uint16_t rlen, uint16_t len)
{
    char             *ptr;
    volatile uint16_t len1, len2, len3;
    uint8_t           tmp;

    if(sscanf(buf, "+MFREAD: %hhu,%hu,%hu,%hu,", &tmp, &len1, &len2, &len3) == 4)    // 解析读取文件响应
    {
        if(tmp != 1) return false;

        if(len1 == rlen && len1 == len2 && len2 == len3 && len1 > 0)    // 如果读取长度一致且大于0
        {
            // 数据在第三个‘,’后面
            if((ptr = strstr(ptr, ",")) == NULL) return false;
            ptr++;
            if((ptr = strstr(ptr, ",")) == NULL) return false;
            ptr++;
            if((ptr = strstr(ptr, ",")) == NULL) return false;
            ptr++;
            memcpy(file, ptr, rlen);    // 将读取文件内容复制到文件缓冲区
        }
    }
    return false;    // 返回false表示解析失败
}

// +MIPRD: 0,0,26,684A004A00680B2032962C000060000004000200000100008616
/// @brief 解析TCP/IP接收数据
/// @note  特殊解析格式
#pragma optimize = none
static R_RESPONSE_t miprd_respone(r_module_data_s *out)
{
    static bool  ret_ok    = false;          // 响应状态
    static bool  ret_parse = false;          // 解析状态
    static bool  ret_error = false;          // 错误状态
    R_RESPONSE_t ret       = RSP_WAITING;    // 响应结果
    uint8_t      buf[R_RXTX_SIZE];           // 接收数据缓冲区
    uint8_t      i = 0;                      // 循环计数器

    r_recv_copy();     // 从串口接收数据到环形缓冲区
    while(++i <= 3)    // 每次最多处理3条消息
    {
        uint16_t msglen;

        memset(buf, 0, sizeof(buf));    // 清空接收数据缓冲区
        if((msglen = get_message(buf, "\r\n", "\r\n")) == 0) { break; }
        att();
        atlog("Recv message: msglen: %d, msg: %s\r\n", msglen, buf);    // 打印消息长度、起始位置和接收数据长度
        if(msglen >= (MIN_MSG_LEN - 2))
        {
            if(mpird_parse((char *)buf, out, msglen)) { ret_parse = true; }
            else if(ok_parse((char *)buf, NULL, msglen)) { ret_ok = true; }
            else if(error_parse((char *)buf, NULL, msglen) != 0xFFFF) { ret_error = true; }
            else { tcp_urc_report(buf); }
        }
    }

    if(ret_parse && ret_ok) { ret = RSP_OK; }
    else if(ret_error || i > 3) { ret = RSP_ERROR; }    // 如果错误状态为true或处理超过3条消息任然没有解析到有效数据,返回RSP_ERROR

    if(ret != RSP_WAITING)
    {
        // 如果响应状态不是等待状态，表示已经处理完毕，重置所有响应状态
        ret_ok    = false;
        ret_parse = false;
        ret_error = false;
    }
    return ret;    // 返回false表示解析失败
}

/// @brief TCP数据接收线程
/// @note 该函数用于接收TCP数据，移植可不修改
static PT_THREAD(tcp_data_recv_thread(struct pt *pt))
{
    static SwTimer_s tim = {0, 0};    // 定时器
    uint16_t         len;
    R_RESPONSE_t     rsp = RSP_WAITING;

    PT_BEGIN(pt);
    r_data.dcu.len   = 0;
    r_data.frame_len = 0;
    r_data.ret       = false;                                            // 重置返回状态
    len              = at_cmd_miprd_creat(dcu_tx, APDU_DATA_LEN_MIN);    // 首次获取17字节，而后解析出数据帧长度
    r_uart_send(dcu_tx, len);                                            // 发送AT命令获取TCP数据长度
    hal_timer.interval(&tim, 100);                                       // 设置定时器间隔为100ms
    PT_WAIT_UNTIL(pt, (rsp = miprd_respone(&r_data)) != RSP_WAITING || hal_timer.expired(&tim));
    if(rsp == RSP_OK)
    {
        if(r_data.dcu.len > 0)
        {
            if(r_data.frame_len == 0)
            {
                // 解析数据帧长度
                r_data.frame_len = api.protocol_len_parse(r_data.dcu.str, r_data.dcu.len, PROTOCOL_10376);    // 解析数据帧长度
                if(r_data.frame_len > r_data.dcusize) { r_data.frame_len = r_data.dcusize; }
            }
            if(r_data.dcu.len >= r_data.frame_len)
            {
                // 接收完成
                r_data.frame_len = 0;
                r_data.ret       = true;    // 设置返回状态为true，表示接收数据成功
                PT_EXIT(pt);                // 退出PT线程
            }
            len = r_data.frame_len - r_data.dcu.len;    // 计算剩余数据长度
            len = at_cmd_miprd_creat(dcu_tx, len);      // 创建AT命令获取剩余数据
            r_uart_send(dcu_tx, len);                   // 发送AT命令获取剩余数据
            hal_timer.interval(&tim, 600);              // 设置定时器间隔为400ms, 实测700字节没问题
            PT_WAIT_UNTIL(pt, (rsp = miprd_respone(&r_data)) != RSP_WAITING || hal_timer.expired(&tim));

            if(r_data.dcu.len)
            {
                // 第二次接收数据，即使数据为0也认为接收完成
                r_data.frame_len = 0;       // 重置数据帧长度
                r_data.ret       = true;    // 设置返回状态为true，表示接收数据成功
                PT_EXIT(pt);                // 退出PT线程
            }
        }
    }
    if(r_data.dcu.len == 0) { r_data.tcp_data_len = 0; }    // 如果接收数据长度为0，重置TCP数据长度
    PT_END(pt);                                             /// TCP数据接收线程结束，退出线程
}

/// @brief 正常运行线程
/// @note 该函数用于处理模块的正常运行逻辑，包括查询TCP数据、接收数据等。移植时无需修改。
/// @param pt PT线程指针
static PT_THREAD(r_module_running(struct pt *pt))
{
    static SwTimer_s tim = {0, 0};    // 定时器
    uint16_t         len = 0;

    PT_BEGIN(pt);

    normal_run_message();
    if(r_data.tcp_data_len == 0)
    {
        // 如果没有TCP数据,每隔2.5秒主动查询一次TCP数据
        if(tim.interval == 0 && tim.start == 0)    // 如果定时器未启动
        {
            hal_timer.interval(&tim, 2500);    // 设置定时器间隔为2.5秒
        }
        if(hal_timer.expired(&tim))
        {
            PT_WAIT_THREAD(pt, module_request(&r_pt.reqpt, 1, 20, AT_MIPRD_LEN));    // 查询TCP数据长度
            hal_timer.restart(&tim);
        }
    }
    if(r_data.tcp_data_len == 0) { PT_EXIT(pt); }    // 如果没有TCP数据，退出线程
    memset(&tim, 0, sizeof(SwTimer_s));              // 清空定时器

    // 如果有TCP数据，处理接收数据
    PT_WAIT_THREAD(pt, tcp_data_recv_thread(&r_pt.recvpt));

    PT_END(pt);    /// 模块运行线程结束，退出线程
}

static bool http_downfile_response(uint32_t read_len)
{
    uint8_t  buf[R_RXTX_SIZE + 1];    // 接收数据缓冲区
    uint16_t len;

    r_recv_copy();    // 从串口接收数据到环形缓冲区
    len = ringbuf_data_len(&r_ringbuf);
    if(len < 200) { return false; }
    if((len = get_message((uint8_t *)buf, "+MHTTPDLFILE: ", "\r\n")) == 0) { return false; }    // 获取HTTP下载文件响应消息
    if(http_dlfile_parse((char *)buf, len, read_len))
    {
        r_data.ret = true;
        return true;
    }    // 解析HTTP下载文件响应

    return false;    // 返回true表示解析成功
}

/// @brief 模块HTTP下载线程
/// @note 该函数用于处理模块的HTTP下载逻辑，包括下载文件等。
/// @param pt PT线程指针
static PT_THREAD(r_module_http_download(struct pt *pt))
{
    uint16_t len;

    PT_BEGIN(pt);

    PT_WAIT_THREAD(pt, module_request(&r_pt.reqpt, 2, 1000, AT_MFCLOSE));    // 先关闭文件
    len = at_cmd_httpdlfile_creat(dcu_tx, r_http.download_start, r_http.download_end);
    ringbuf_reset(&r_ringbuf);    // 重置接收缓冲区
    r_uart_send(dcu_tx, len);
    r_data.ret = false;
    PT_WAIT_UNTIL(pt, http_downfile_response(r_http.download_end - r_http.download_start + 1) || !PT_SCHEDULE(thread_delay(&r_pt.atpt, 40000)));
    if(r_data.ret)    // 如果下载成功
    {
        r_data.ret = false;    // 重置返回状态
    }
    else
    {
        logd("\r\nHTTP download file failed\r\n");
        PT_WAIT_THREAD(pt, thread_delay(&r_pt.atpt, 500));
        PT_EXIT(pt);    // 退出PT线程
    }

    PT_END(pt);    /// 模块运行线程结束，退出线程
}

/// @brief 初始化函数
/// @param rxbuf
/// @param bufsize
static void r_module_init(uint8_t *rxbuf, uint16_t bufsize)
{
    r_pt_init();                                      // 初始化PT线程
    r_rbuf_clear();                                   // 清空接收缓冲区
    memset(&r_data, 0, sizeof(r_module_data_s));      // 清空模块参数结构体
    memset(&r_state, 0, sizeof(r_module_state_s));    // 清空模块状态参数结构体
    r_data.dcu.str    = rxbuf;                        // 设置接收缓冲区指针
    r_data.dcu.len    = 0;                            // 初始化接收缓冲区长度
    r_data.dcusize    = bufsize;                      // 设置接收缓冲区大小
    r_data.task_state = M_TASK_INIT;                  // 初始化任务状态
    // 初始化模块
    hal_gpio.remote_module(GPIO_OPEN);
    r_uart_open();    // 打开串口
    logd("\r\nCat1 Module initialized\r\n");
}

/// @brief 启动重新配置模块
static void r_module_restart(void)
{
    logd("\r\nCat1 Module restarted\r\n");
}

static void r_module_access_set(void)
{
    // 有接收到数据 || 者串口发送正在进行中 || 或者任务状态不是正常运行，直接返回
    if(r_uart_send_over_query() == false || r_data.tcp_data_len || r_data.task_state != M_TASK_NORMAL) { return; }
    switch(r_request_type)
    {
        case REQUEST_RTC_SYNC:
            r_data.task_state = M_TASK_NTP_SYNC;    // 设置任务状态为NTP同步
            break;
        case REQUEST_DATA_SYNC:
            r_data.task_state = M_TASK_INFO_GET;    // 设置任务状态为获取模块信息
            break;
        case REQUEST_FILE_RECV:
            r_data.task_state = M_TASK_FILE_RECV;    // 设置任务状态为文件接收
            break;
        case REQUEST_TCP_OPEN:
            r_data.task_state = M_TASK_TCP;    // 设置任务状态为TCP连接
            break;
        default:
            break;
    }
    r_request_type = REQUEST_NONE;    // 重置请求类型
    if(r_state.tcp0_state == false)
    {
        r_data.task_state = M_TASK_TCP;    // 如果TCP连接状态为false，设置任务状态为TCP连接
    }
}

/// @brief 模块主线程
/// @note 该函数用于处理模块的主线程逻辑，包括初始化、配置、TCP连接、获取模块信息等。移植时无需修改。
/// @param pt PT线程指针
static PT_THREAD(r_module_main_thread(struct pt *pt))
{
    PT_BEGIN(pt);

    // 运行任务
    while(1)
    {
        if(r_data.task_state == M_TASK_NORMAL)    // 如果任务状态为初始化
        {
            PT_SPAWN(pt, &r_pt.mpt, r_module_running(&r_pt.mpt));
            if(!r_data.ret) r_module_access_set();    // 无应用层协议数据时，查询子任务
            break;                                    // 重置返回状态
        }
        else if(r_data.task_state == M_TASK_INIT)
        {
            logd("\r\nModule main thread started\r\n");
            PT_WAIT_THREAD(pt, thread_delay(&r_pt.atpt, 1));
            r_data.task_state = M_TASK_CFG;    // 转到配置任务
        }
        else if(r_data.task_state == M_TASK_CFG)
        {
            // 配置模块
            PT_SPAWN(pt, &r_pt.mpt, r_module_configure_thread(&r_pt.mpt));    //
            if(r_data.task_state == M_TASK_INIT || r_data.task_state == M_TASK_CFG)
            {
                logd("\r\nModule configuration failed, exiting\r\n");
                r_module_restart();    // 重新配置模块
                PT_EXIT(pt);           // 如果配置失败，退出线程
            }
            r_data.task_state = M_TASK_TCP;
            logd("\r\nModule configuration completed\r\n");
            break;
        }
        else if(r_data.task_state == M_TASK_TCP)
        {
            // TCP连接
            PT_SPAWN(pt, &r_pt.mpt, r_module_tcp_connection(&r_pt.mpt));    // 启动TCP连接线程
            if(r_data.task_state == M_TASK_INIT || r_data.task_state == M_TASK_CFG)
            {
                r_module_restart();    // 重新配置模块
                PT_EXIT(pt);           // 如果TCP连接失败，退出线程
            }
            else if(r_data.task_state == M_TASK_TCP)
            {
                PT_EXIT(pt);    // 如果TCP连接失败，网络正常，重试tcp连接
            }
            r_state.login_req = true;               // tcp连接成功，使能应用层协议登录请求
            r_data.task_state = M_TASK_INFO_GET;    // 获取模块信息
        }
        else if(r_data.task_state == M_TASK_INFO_GET)
        {
            // 获取模块信息
            PT_SPAWN(pt, &r_pt.mpt, r_module_info_get(&r_pt.mpt));    // 启动获取模块信息线程
            r_request_type    = REQUEST_NONE;                         // 重置请求类型
            r_data.task_state = M_TASK_NORMAL;                        // 转到NTP任务状态
        }
        else if(r_data.task_state == M_TASK_NTP_SYNC)
        {
            // 同步网络时间
            PT_SPAWN(pt, &r_pt.mpt, r_module_network_time_sync(&r_pt.mpt));    // 启动网络时间同步线程
            r_request_type    = REQUEST_NONE;                                  // 重置请求类型
            r_data.task_state = M_TASK_NORMAL;                                 // 转到正常任务状态
        }
        else
        {
            logd("\r\nModule main thread: Unknown task state %d\r\n", r_data.task_state);    // 打印未知任务状态
            r_data.task_state = M_TASK_INIT;                                                 // 重置任务状态为初始化
            r_module_restart();                                                              // 重新配置模块
            PT_EXIT(pt);                                                                     // 退出线程
        }
    }
    PT_END(pt);
}

uint16_t r_module_recv(void)
{
    uint16_t len = 0;    // 接收数据长度

    r_module_main_thread(&r_pt.main_pt);                               // 启动模块主线程
    if(!r_data.ret || r_data.task_state != M_TASK_NORMAL) return 0;    // 如果没有接收到数据或者当前任务状态不是接收任务
    r_data.ret     = false;                                            // 重置接收标志
    len            = r_data.dcu.len;                                   // 获取接收数据长度
    r_data.dcu.len = 0;                                                // 清空接收缓冲区长度

    return len;    // 返回接收数据长度
}

/// @brief 模块发送数据函数
/// @note 该函数用于发送数据到模块，
void r_module_send(uint8_t *msg, uint16_t len)
{
    r_at_cmd_s *cmd_ptr          = (r_at_cmd_s *)&r_at_cmd_list[AT_MIPSEND];    // 获取AT命令结构体指针
    uint8_t     buf[R_RXTX_SIZE] = {0};                                         // 发送缓冲区

    if(msg == NULL || len == 0) return;          // 如果消息为空或者长度为0，直接返回
    hexstr_to_charstr((char *)buf, msg, len);    // 将消息转换为字符串格式
    // len = vsnprintf((char *)dcu_tx, sizeof(dcu_tx), cmd_ptr->str, len, buf);
    len = sprintf((char *)dcu_tx, cmd_ptr->str, len, buf);
#if P_REMOTE_MODULE
    if(len < sizeof(dcu_tx)) { dcu_tx[len] = '\0'; }    // 确保AT命令以'\0'结尾
#endif
    atlog("\r\nModule sent data: len: %u, msg: %s\r\n", len, dcu_tx);
    r_uart_send(dcu_tx, len);    // 发送数据到模块
}

/// @brief 模块最后上报函数
/// @note 该函数用于处理模块最后上报数据，主要用于掉电上报。 上报时已关中断，只能用polling方式发送数据
/// @param msg 消息指针
/// @param len 消息长度
/// @param typ 类型 0- 重新打开串口，1-发送数据
void r_module_lastgasp_send(uint8_t *msg, uint16_t len, uint8_t typ)
{
    if(typ == 0)
    {
        r_uart_close();           // 关闭串口
        r_uart_open_polling();    // 重新打开串口，模式为轮询模式
    }
    else
    {
        r_at_cmd_s *cmd_ptr = (r_at_cmd_s *)&r_at_cmd_list[AT_MIPSEND];    // 获取AT命令结构体指针

        len = sprintf((char *)dcu_tx, cmd_ptr->str, msg);
        for(uint16_t i = 0; i < len; i++)    // 遍历消息长度
        {
            r_uart_print(dcu_tx[i]);    // 逐字节发送消息
        }
    }
}

bool r_module_access_request(REQUEST_TYPE_t typ)
{
    if(r_request_type != REQUEST_NONE) return false;    // 如果当前请求类型不是无请求，直接返回
    r_request_type = typ;                               // 设置当前请求类型
    return true;
}

MODULE_TASK_t r_module_task_get(void)
{
    return r_data.task_state;    // 返回当前任务状态
}

//
