/**
 ******************************************************************************
 * @file    dcu.c
 * <AUTHOR> @date    2025
 * @brief   dcu单元处理远程数据交互
 *
 ******************************************************************************
 *
 * @note
 * Copyright (C) 2025  SheWei Electrics Co.,Ltd. All rights reserved.
 *
 *
 ******************************************************************************/

#include <string.h>
#include "utils.h"
#include "status.h"
#include "dcu.h"
#include "power_event.h"
#include "pt.h"
#include "DLT645_2007.h"
#include "module_para.h"
#include "bsp.h"
#include "debug.h"
#include "api.h"
#include "..\protocol\QGWD10376\QGDW10376.h"

#define logd(...) DBG_PRINTF(P_DCU, D, __VA_ARGS__)    // 模块日志打印宏
#define logm(...) DBG_PRINTF(P_DCU, M, __VA_ARGS__)    // 通讯数据打印
#define logt(...) DBG_PRINTF(P_DCU, T, __VA_ARGS__)    // 时间戳打印

typedef struct dcu_pt_struct
{
    struct pt mpt;
    struct pt sub;
    struct pt wait;    // 等待PT线程
} dcu_pt_s;

/// variables
static dcu_state_s      dcu_state;                          // DCU状态
static uint8_t          dcu_buf[DCU_DATA_BUF_SIZE + 32];    // DCU数据缓存
static SwTimer_s        dcu_timer;                          // DCU定时器
static TYPE_DCU_EVT_OUT dcu_evt_out;                        // 事件输出状态字
static dcu_pt_s         dcu_pt;                             // DCU处理线程
static SwTimer_s        heartbeat_tmr;                      // 心跳定时器
static SwTimer_s        module_outtimer;                    // 模块超时定时器

CM_STATUS_s cm_status;    // 通讯模块状态

void dcu_init(void)
{
    PT_INIT(&dcu_pt.mpt);                       // 初始化DCU处理线程
    PT_INIT(&dcu_pt.sub);                       // 初始化子线程
    PT_INIT(&dcu_pt.wait);                      // 初始化等待线程
    remote_m.init(dcu_buf, sizeof(dcu_buf));    // 初始化远程模块
    module_para.init();                         // 初始化模块参数
}

static PT_THREAD(thread_delay(struct pt *pt, uint32_t ms))
{
    static SwTimer_s pt_tmr;    // 获取PT定时器

    PT_BEGIN(pt);
    hal_timer.interval(&pt_tmr, ms);                  // 设置定时器
    PT_WAIT_UNTIL(pt, hal_timer.expired(&pt_tmr));    // 等待定时器到期
    PT_END(pt);
}

/// @brief  dcu登录ack确认函数
/// @note   该函数用于处理DCU登录ack确认
/// @param
/// @return
static bool dcu_login_ack(void)
{
    uint16_t len = remote_m.recv();    // 获取接收数据长度

    if(len && gdw376.login_ack(dcu_buf, len))    // 如果登录ack确认成功
    {
        dcu_state.login_ok = true;    // 设置DCU登录状态
        return true;                  // 返回登录成功状态
    }
    return false;    // 返回登录失败状态
}

PT_THREAD(dcu_login_request(struct pt *pt))
{
    static SwTimer_s tmr;

    PT_BEGIN(pt);
    dcu_state.access_module = true;
    dcu_state.login_ok      = false;    // 清除DCU登录状态
    logt();
    logd("DCU login request...\r\n");                                 // 打印登录请求日志
    remote_m.send(dcu_buf, gdw376.get_login_frame(dcu_buf));          // 执行登录请求
    hal_timer.interval(&tmr, 3000);                                   // 设置定时器
    PT_WAIT_UNTIL(pt, dcu_login_ack() || hal_timer.expired(&tmr));    // 等待登录ack确认或定时器超时

    if(!dcu_state.login_ok)    // 如果登录失败
    {
        logt();
        logd("DCU login failed!\r\n");                 // 打印登录失败日志
        remote_m.state->login_req = true;              // 保持登录请求状态
        hal_timer.interval(&tmr, 20 * 1000);           // 设置定时器
        PT_WAIT_UNTIL(pt, hal_timer.expired(&tmr));    // 等待定时器超时, 20秒后重试登录
    }
    else
    {
        remote_m.state->login_req = false;                    // 清除登录请求状态
        dcu_state.heartbeat       = true;                     // 设置心跳包发送需求
        hal_timer.interval(&heartbeat_tmr, 4 * 60 * 1000);    // 设置心跳定时器间隔
        logt();
        logd("DCU login success!\r\n");    // 打印登录成功日志
    }

    PT_END(pt);
}
/// @brief  dcu登录ack确认函数
/// @note   该函数用于处理DCU登录ack确认
/// @param
/// @return
static bool dcu_heartbeat_ack(void)
{
    uint16_t len = remote_m.recv();    // 获取接收数据长度

    if(len && gdw376.heartbeat_ack(dcu_buf, len))
    {
        dcu_state.heartbeat_ok = true;    // 设置DCU心跳状态
        return true;                      // 返回心跳成功状态
    }
    return false;    // 返回心跳失败状态
}

PT_THREAD(dcu_heartbeat_request(struct pt *pt))
{
    static SwTimer_s tmr;
    static uint8_t   heartbeat_err = 0;

    PT_BEGIN(pt);
    dcu_state.access_module = true;
    dcu_state.heartbeat_ok  = false;    // 清除心跳成功状态
    logt();
    logd("DCU heartbeat request...\r\n");                                 // 打印心跳请求日志
    remote_m.send(dcu_buf, gdw376.get_heartbeat_frame(dcu_buf));          // 执行登录请求
    hal_timer.interval(&tmr, 5000);                                       // 设置定时器,4.5分钟
    PT_WAIT_UNTIL(pt, dcu_heartbeat_ack() || hal_timer.expired(&tmr));    // 等待登录ack确认或定时器超时

    if(!dcu_state.heartbeat_ok)    // 如果心跳失败
    {
        heartbeat_err++;                                                        // 增加心跳错误计数
        if(heartbeat_err > 6) { remote_m.access_request(REQUEST_TCP_OPEN); }    // 如果心跳错误计数超过6次，设置访问请求状态为TCP连接,tcp连接失败后会重启模块
        logt();
        logd("DCU heartbeat failed!\r\n");             // 打印心跳失败日志
        dcu_state.heartbeat = true;                    // 保持心跳请求状态
        hal_timer.interval(&tmr, 10 * 1000);           // 设置定时器
        PT_WAIT_UNTIL(pt, hal_timer.expired(&tmr));    // 等待定时器超时, 10秒后重试心跳
    }
    else
    {
        heartbeat_err          = 0;        // 清除心跳错误计数
        dcu_state.heartbeat    = false;    // 清除心跳请求状态
        dcu_state.heartbeat_ok = true;     // 清除心跳成功状态
        logt();
        logd("DCU heartbeat success!\r\n");    // 打印心跳成功日志
    }

    PT_END(pt);
}

PT_THREAD(dcu_module_chk(struct pt *pt))
{
    static SwTimer_s tmr;    // 定义定时器

    PT_BEGIN(pt);

    if(remote_m.send_over_query() == false)    // 如果模块发送未完成
    {
        hal_timer.interval(&tmr, 5000);    // 5秒超时，初始化模块
        PT_WAIT_UNTIL(pt, remote_m.send_over_query() || hal_timer.expired(&tmr));
        if(remote_m.send_over_query() == false)    // 如果模块发送仍未完成
        {
            logd("\r\nDCU module send timeout!\r\n");
            remote_m.init(dcu_buf, sizeof(dcu_buf));    // 初始化远程模块
            PT_EXIT(pt);
        }
        PT_EXIT(pt);    // 退出PT线程
    }

    PT_END(pt);
}

/// @brief 停电上报，先把串口改为
/// @param
void dcu_lastgasp(uint8_t typ)
{
    if(typ == 0)
    {
        remote_m.lastgasp_send(NULL, 0, 0);    //  关闭串口，再打开，polling方式发送停电上报
    }
    else
    {
        remote_m.lastgasp_send(dcu_buf, gdw376.get_lastgasp_frame(dcu_buf), 1);    // 发送停电上报数据
    }
}

char dcu_process(void)
{
    uint16_t len = 0;

    PT_BEGIN(&dcu_pt.mpt);    // 开始PT线程

    if(hal_timer.expired(&heartbeat_tmr))    // 如果心跳定时器已过期
    {
        dcu_state.heartbeat = true;           // 设置心跳包发送需求
        hal_timer.restart(&heartbeat_tmr);    // 重置心跳定时器间隔
    }

    PT_SPAWN(&dcu_pt.mpt, &dcu_pt.sub, dcu_module_chk(&dcu_pt.sub));    // 数据没有发完不处理新数据。

    len = remote_m.recv();    // 接收远程模块数据

    // 如果TCP连接状态为false
    if(remote_m.state->tcp0_state == false || remote_m.task_state() != M_TASK_NORMAL) { PT_EXIT(&dcu_pt.mpt); }

    if(remote_m.state->login_req && !((dcu_state.access_module == false) && len))  // 非访问状态，有收到有效数据，直接去解析协议
    {
        PT_SPAWN(&dcu_pt.mpt, &dcu_pt.sub, dcu_login_request(&dcu_pt.sub));    // 启动DCU登录请求线程
        dcu_state.access_module = false; 
        PT_EXIT(&dcu_pt.mpt);
    }
    else if(dcu_state.login_ok && dcu_state.heartbeat && !((dcu_state.access_module == false) && len)) // 非访问状态，有收到有效数据，直接去解析协议
    {
        PT_SPAWN(&dcu_pt.mpt, &dcu_pt.sub, dcu_heartbeat_request(&dcu_pt.sub));    // 启动DCU心跳请求线程
        dcu_state.access_module = false; 
#if P_DCU
        {
            static uint8_t i = 0;

            i = !i;
            if(i)
                remote_m.access_request(REQUEST_RTC_SYNC);    // 清除访问请求状态
            else
                remote_m.access_request(REQUEST_DATA_SYNC);    // 设置访问请求状态
        }
#endif
        PT_EXIT(&dcu_pt.mpt);
    }

    if(!len) { PT_EXIT(&dcu_pt.mpt); }    // 如果没有接收到数据，退出PT线程

    logd("\r\nDCU recv data %hu: \r\n", len);               // 打印接收数据日志
    logm(dcu_buf, len);                                     // 打印接收数据内容
    if((len = gdw376.msg_process(0, dcu_buf, len)) != 0)    // 处理接收到的数据
    {
        // 如果处理后的数据长度不为0，表示有数据需要发送
        logd("\r\nDCU send data %hu :\r\n", len);
        logm(dcu_buf, len);
        remote_m.send(dcu_buf, len);
    }

    PT_END(&dcu_pt.mpt);    // PT线程结束，退出线程
}

const struct dcu_s dcu = {
    .init     = dcu_init,        // 初始化函数
    .process  = dcu_process,     // 处理函数
    .lastgasp = dcu_lastgasp,    // 停电上报函数
};

// file end
