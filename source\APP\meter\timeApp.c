
#include "timeapp.h"
#include "hal_rtc.h"
#include "crc.h"
#include "datastore.h"
#include "bsp.h"
#include "utils.h"
#include "app_config.h"

#define CLOCK_CRC16          0  ///只能在测试阶段修改，产品发布后不得再修改！！！！否则会丢失数据
#define CRC16_CHK(struct,len)    STRUCT_CRC16_CHK(CLOCK_CRC16, struct, len)
#define CRC16_CAL(struct,len)    STRUCT_CRC16_GET(CLOCK_CRC16, struct, len)

#define CLOCK_PARA_ADDR      nvm_addr(NVM_CLOCK_PARA) // 时钟参数存储区地址
#define CLOCK_DATA_ADDR      nvm_addr(NVM_CLOCK_DATA) // 时钟数据存储区地址
#define CLOCK_PD_ADDR        nvm_addr(NVM_CLOCK_PD)   // 时钟数据掉电时存储区地址


extern const ClockPara_s  clock_default_para;
static const ClockPara_s* clock_running_para; ///时钟运行参数
static CLOCK_STUS clock_out_stus;             ///事件输出状态字
static ClockData_s clock_data;                ///时钟运行记录数据
static uint16_t clock_checkin_cnt;            ///时钟运行记录checkin周期秒计数器
static bool f_secpulse = FALSE;
clock_s     g_clock;      //当前时钟数据

uint8_t get_week_value(uint16_t year, uint8_t month, uint8_t day);
bool is_calendar_date_no_equal(const Calendar_s* calendar1, const Calendar_s* calendar2);
void clock_update(void);

/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
static const uint8_t cMonthDays[] = {29, 31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};
static const uint8_t cMonthFirstDay[] = {0, 3, 3, 6, 1, 4, 6, 2, 5, 0, 3, 5};
static const Calendar_s default_calendar =  ///默认时间2024-1-1 星期一 00:00:00
{
	.date = {24, 1, 1, 1},
	.time = {0, 0, 0},
};

static bool clock_para_checkin(uint16_t ofst, const void* val, uint16_t len)
{
    ClockPara_s para;
    if(ofst != 0) memcpy(&para, clock_running_para, sizeof(para));
    memcpy((uint8_t*)&para + ofst, val, len);
	CRC16_CAL(&para, sizeof(ClockPara_s));
    clock_running_para = (const ClockPara_s*)CLOCK_PARA_ADDR;
	return nvm.write((uint32_t)clock_running_para, &para, sizeof(para));
}

/// @brief
/// @param
static void clock_para_recover(void)
{
    clock_running_para = (const ClockPara_s*)CLOCK_PARA_ADDR;
    if(CRC16_CHK(clock_running_para, sizeof(ClockPara_s)) ==  FALSE)
	{
        clock_running_para = &clock_default_para;
	}
}

/// @brief
/// @param
static void clock_data_checkin(void)
{
	clock_checkin_cnt = 0;
	clock_data.pwrdn_time = g_clock;
    CRC16_CAL(&clock_data, sizeof(ClockData_s));
    nvm.write(CLOCK_DATA_ADDR, &clock_data, sizeof(ClockData_s));
}

/// @brief 正常运行时时钟数据校验
/// @param
static void clock_data_recover(void)
{
    if(CRC16_CHK(&clock_data, sizeof(ClockData_s)) == FALSE)
    {
        nvm.read(CLOCK_DATA_ADDR, &clock_data, sizeof(ClockData_s));     
        if(CRC16_CHK(&clock_data, sizeof(ClockData_s)) == FALSE)
        {
            nvm.read(CLOCK_PD_ADDR, &clock_data, sizeof(ClockData_s));    
            if(CRC16_CHK(&clock_data, sizeof(ClockData_s)) == FALSE)
            {
                memset(&clock_data, 0, sizeof(ClockData_s));
                clock_data.pwrdn_time.cale                = default_calendar;
                clock_data.pwrdn_time.stus.doubtful_value = true;
                CRC16_CAL(&clock_data, sizeof(ClockData_s));
                nvm.write(CLOCK_DATA_ADDR, &clock_data, sizeof(ClockData_s));   
            }
        }
    }
}
/// @brief 初始化时钟数据恢复
/// @param  
static void clock_data_init(void)
{
    Calendar_s clock_tmp = g_clock.cale;
    if(CRC16_CHK(&clock_data, sizeof(ClockData_s)) == FALSE)
    {
        nvm.read(CLOCK_PD_ADDR, &clock_data, sizeof(ClockData_s));    // 先恢复掉电保存的时钟
        if(CRC16_CHK(&clock_data, sizeof(ClockData_s)) == FALSE)
        {
            nvm.read(CLOCK_DATA_ADDR, &clock_data, sizeof(ClockData_s));    // 掉电保存异常恢复上电保存的时钟
            if(CRC16_CHK(&clock_data, sizeof(ClockData_s)) == FALSE)
            {
                memset(&clock_data, 0, sizeof(ClockData_s));
                clock_data.pwrdn_time.cale                = default_calendar;
                clock_data.pwrdn_time.stus.doubtful_value = true;
                CRC16_CAL(&clock_data, sizeof(ClockData_s));
                nvm.write(CLOCK_DATA_ADDR, &clock_data, sizeof(ClockData_s));    // 保证掉电时钟有存储记录
            }
        }
    }
    if(is_calendar_date_no_equal(&clock_data.pwrdn_time.cale, &clock_tmp))
    {
        clock_data.bc_num     = 0;
        clock_data.bc_err_num = 0;
        CRC16_CAL(&clock_data, sizeof(ClockData_s));
        nvm.write(CLOCK_DATA_ADDR, &clock_data, sizeof(ClockData_s));
    }
}


/// @brief 闰年判断
/// @param year 
/// @return 
static bool is_leap_year(uint8_t year)
{
    return (year % 4) == 0;
}

/* 返回指定年的某月天数 */

/// @brief 获取某年某月份的天数
/// @param year 
/// @param month 
/// @return 天数
uint8_t get_month_days(uint16_t year, uint8_t month)
{
    if(is_leap_year(year))
    {
        if(month == 2) month = 0;
    }
    return cMonthDays[month];
}

/// @brief 根据date，转换为相应的天数，2000.1.1
/// @param date 
/// @return 天数
static uint16_t date_to_days(const CalendarDate_s* date)
{
    uint8_t i;
    uint16_t days = 0;

    if(date->year == 0)  //为00年
    {
        days = 0;
    }
    else
    {
        days = (date->year) / 4 + (uint32_t)date->year * 365;
        if((date->year) % 4) days += 1;
    }

    if(is_leap_year(date->year) && date->month > 2)
    {
        days += 1;
    }

    for(i = 1; i < date->month; i++)
    {
        days += cMonthDays[i];
    }

    days += date->day - 1; // 当日不算在天数内

    return days;
}

/// @brief 根据天数，转换为date,
/// @param days 
/// @return date
static CalendarDate_s days_to_date(uint16_t days)
{
    CalendarDate_s date;
    date.year  = 0;
    date.month = 1;
    date.day   = 1;
    while(days > 0)
    {
        uint16_t day_unit;
        if(is_leap_year(date.year))
        {
            day_unit = 366;
        }
        else
        {
            day_unit = 365;
        }
        if(days >= day_unit)
        {
            if(date.year >= 99) date.year = 0;
            else date.year += 1;
            days -= day_unit;
        }
        else
        {
            day_unit = get_month_days(date.year, date.month);
            if(days >= day_unit)
            {
                date.month += 1;
                days -= day_unit;
                continue;
            }
            else
            {
                date.day += days;
                days = 0;
            }
        }
    }
    date.week = get_week_value(BASE_YEAR + date.year, date.month, date.day);
    return date;
}

/// @brief 把时间转换成秒数
/// @param time 
/// @return 
static uint32_t time_to_seconds(const CalendarTime_s* time)
{
    return (uint32_t)(time->hour) * 3600 + (uint32_t)(time->minute) * 60 + (time->second);
}

/// @brief 以秒数反推算出时间
/// @param seconds 
/// @return 
static CalendarTime_s seconds_to_time(uint32_t seconds)
{
    CalendarTime_s time;
    time.second = (uint8_t)(seconds % 60);
    seconds /= 60;
    time.minute = (uint8_t)(seconds % 60);
    seconds /= 60;
    time.hour   = (uint8_t)(seconds % 24);
    return time;
}

/* 把日期时间转换成相应的秒数 **/
uint32_t calendar_to_seconds(const Calendar_s* calendar)
{
    return time_to_seconds(&calendar->time) + (uint32_t)date_to_days(&calendar->date) * 86400;
}

/* 把秒数反推算日期时间 **/
void seconds_to_calendar(Calendar_s* calendar, uint32_t seconds)
{
    uint16_t totaldays;
    totaldays = seconds / (60 * 60 * 24L);
    seconds   = seconds % (60 * 60 * 24L);
    calendar->date = days_to_date(totaldays);
    calendar->time = seconds_to_time(seconds);
}

/// @brief 时间比对
/// @param dec 
/// @param src 
/// @return dec < src: -1, dec == src: 0, dec > src: 1
static int8_t clock_cmp(clock_s dec, clock_s src)
{
	if(src.year > dec.year) return -1;
	else if(src.year < dec.year) return 1;
	if(src.month > dec.month) return -1;
	else if(src.month < dec.month) return 1;
	if(src.day > dec.day) return -1;
	else if(src.day < dec.day) return 1;
	if(src.hour > dec.hour) return -1;
	else if(src.hour < dec.hour) return 1;
	if(src.minute > dec.minute) return -1;
	else if(src.minute < dec.minute) return 1;
	if(src.second > dec.second) return -1;
	else if(src.second < dec.second) return 1;
	return 0;
}

/// @brief 判断时间格式是否正确
bool is_calendar_validis(const Calendar_s* calendar)
{
    if(calendar->time.hour   > 23) return FALSE;
    if(calendar->time.minute > 59) return FALSE;
    if(calendar->time.second > 59) return FALSE;
    if(calendar->date.year   > 99) return FALSE;
    if(calendar->date.month  > 12 || calendar->date.month == 0) return FALSE;
    if(calendar->date.day == 0 || \
       calendar->date.day > get_month_days(calendar->date.year, calendar->date.month)) return FALSE;
    
    return TRUE;
}

/// @brief 判断两个日期是否相等
bool is_calendar_date_no_equal(const Calendar_s* calendar1, const Calendar_s* calendar2)
{
    if(calendar1->date.year != calendar2->date.year)  return TRUE;
    if(calendar1->date.month!= calendar2->date.month) return TRUE;
    if(calendar1->date.day  != calendar2->date.day)   return TRUE;
    return FALSE;
}

/// 根据年月日计算星期
uint8_t get_week_value(uint16_t year, uint8_t month, uint8_t day)
{
    uint16_t week;
    if(year < 100) year += BASE_YEAR;
    if(month == 0 || day == 0) return 0xFF;
    week = year % 7;
    week = week + (year - 1) / 4;
    week = week - (year - 1) / 100;
    week = week + (year - 1) / 400;
    week = week + cMonthFirstDay[month - 1];
    if(month > 2 && (year % 4) == 0) { ++week; }
    week = (week + (day - 1)) % 7;
#if RTC_SUNDAY_IS_0
    return (uint8_t)week;                        // 星期日为0
#else
    return (uint8_t)((week == 0) ? 7 : week);    // 星期日为7
#endif
}

/// @brief 从MCU获取时间日期
/// @param dt 
bool hw_rtc_get(Calendar_s *dt)
{
    struct rtc_t rtc;

    if(!hal_rtc.time_get(&rtc)) return false;  //读取错误
    memcpy(&dt->date.year, &rtc.YY, 3);
    memcpy(&dt->time.hour, &rtc.hh, 3);
    dt->date.week   = get_week_value(dt->date.year, dt->date.month, dt->date.day);
    g_clock.u32datetime = calendar_to_seconds(dt);
    return true;
}

/// @brief 时间日期写入MCU内部RTC
/// @param dt 
/// @return 
bool hw_rtc_set(Calendar_s *dt)
{
    struct rtc_t rtc;

    memcpy(&rtc.YY, &dt->date.year, 3);
    memcpy(&rtc.hh, &dt->time.hour, 3);
    dt->date.week = get_week_value(dt->date.year, dt->date.month, dt->date.day);
    
    DBG_PRINTF(P_CLOCK, D, "\r\n set clock : 20%02d-%02d-%02d %01d, %02d:%02d:%02d", rtc.YY, rtc.MM, rtc.DD, dt->date.week, rtc.hh, rtc.mm, rtc.ss);
    g_clock.u32datetime = calendar_to_seconds(dt);
#if P_CLOCK
{
    Calendar_s tmp;
    seconds_to_calendar(&tmp, g_clock.u32datetime);
    tmp.date.week = get_week_value(tmp.date.year, tmp.date.month, tmp.date.day);
    DBG_PRINTF(P_CLOCK, D, "\r\n seconds to datetime: 20%02d-%02d-%02d %01d, %02d:%02d:%02d", tmp.date.year, tmp.date.month, tmp.date.day, tmp.date.week, tmp.time.hour, tmp.time.minute, tmp.time.second);
}
#endif
    
    return (hal_rtc.time_set(&rtc));
}

/// @brief 初始化
/// @param  
void clock_init(void)
{
    clock_s tmpClock;

    clock_para_recover();
    clock_data_init();

    /// 时钟初始化
	g_clock.stus = clock_data.pwrdn_time.stus;

    if(hw_rtc_get(&tmpClock.cale))
    {
        tmpClock.u32datetime = calendar_to_seconds(&tmpClock.cale);
        //当前时钟比备份时钟小 或者 当前时间大于备份时钟时间1000天, 获取的硬件时间格式错误
        if(is_calendar_validis(&clock_data.pwrdn_time.cale)) 
        {
            if( (is_calendar_validis(&tmpClock.cale) == FALSE) || \
                (tmpClock.u32datetime   < clock_data.pwrdn_time.u32datetime) ||\
                ((tmpClock.u32datetime  > clock_data.pwrdn_time.u32datetime) && \
                 ((tmpClock.u32datetime - clock_data.pwrdn_time.u32datetime) > 1000 * 86400)))
            {
                g_clock.cale = clock_data.pwrdn_time.cale;  // 恢复到备份时钟
                g_clock.stus.doubtful_value = TRUE;         // 置时钟可疑状态

                hw_rtc_set(&g_clock.cale);
                clock_out_stus |= STUS_CLOCK_LOSS;
            }
        }
        else if(!clock_data.pwrdn_time.stus.doubtful_value) // 若备份时钟错了，RTC置位可疑
        {
            g_clock.stus.doubtful_value = TRUE;    // 置时钟可疑状态
            clock_out_stus |= STUS_CLOCK_LOSS;
        }
    }

    hal_rtc.ppm_wr((int16_t)clock_running_para->rtc_ppm);    // 校正RTC

    clock_update();
}
/// @brief 放置于秒中断
/// @param  
void clock_update(void)
{
    static Calendar_s clock_tmp;
    clock_para_recover();
    clock_data_recover();

    clock_tmp = g_clock.cale;
	if(!hw_rtc_get(&g_clock.cale))
	{
		/* 获取备份的时钟 */
		if(!is_calendar_validis(&clock_data.pwrdn_time.cale))
		{
			g_clock.cale = default_calendar;        // 备份时钟无效, 恢复到默认时钟
			g_clock.stus.invalid_value = TRUE;      // 置时钟无效状态
			clock_out_stus |= STUS_CLOCK_INVALID;
		}
		else
		{
			g_clock.cale = clock_data.pwrdn_time.cale; // 恢复到备份时钟
			g_clock.stus.doubtful_value = TRUE;        // 置时钟可疑状态
		}
        hw_rtc_set(&g_clock.cale);
        clock_out_stus |= STUS_CLOCK_LOSS;
        clock_data_checkin();
	}
    else
    {
        if(is_calendar_date_no_equal(&g_clock.cale, &clock_tmp) && \
          clock_data.bc_num && \
          clock_data.bc_err_num)
        {
            clock_data.bc_num     = 0;
            clock_data.bc_err_num = 0;
            clock_checkin_cnt = 0;
            CRC16_CAL(&clock_data, sizeof(ClockData_s));
            nvm.write(CLOCK_DATA_ADDR, &clock_data, sizeof(ClockData_s));
        }
    }
    /// 15分钟备份一次时钟
    if((bsp.state_query(STUS_BSP_PWR_ON)) && (++clock_checkin_cnt > 15 * 60)) 
    { 
        clock_data_checkin();
    }
}
/// @brief 时钟模块数据掉电保存
void clock_pwr_down_save(void)
{
    clock_data.pwrdn_time = g_clock;
    CRC16_CAL(&clock_data, sizeof(ClockData_s));
	nvm.write(CLOCK_PD_ADDR, &clock_data, sizeof(ClockData_s));
}
/// @brief 时钟模块数据复位
/// @param type 复位类型
void clock_reset(uint8_t type)
{
    if(type & SYS_PARA_RESET)
    {
        /* 恢复默认参数 */
        clock_para_checkin(0, &clock_default_para, sizeof(ClockPara_s));
    }
    if(type & SYS_DATA_RESET)
    {
        clock_data.shift_time.stus.value = 0xFF;
        clock_data.bc_time               = g_clock;
        clock_data_checkin();

        clock_out_stus = 0;
    }
}
/// @brief 时钟模块状态查询
bool clock_state_query(CLOCK_STUS state)
{
    return boolof(clock_out_stus & state);
}
/// @brief 时钟模块状态清除
void clock_state_clr(void)
{
    clock_out_stus = 0;
}
/// @brief 时钟模块参数获取
const ClockPara_s* clock_para_get(void)
{
    return clock_running_para;
}
/// @brief 时钟模块参数设置
bool clock_para_set(uint16_t ofst, const void* val, uint16_t len)
{
    return clock_para_checkin(ofst, val, len);
}
/// @brief 上次掉电时间获取
void clock_pdtime_get(clock_s* clock)
{
    *clock = clock_data.pwrdn_time;
}
/// @brief 上次校时前时间获取
void clock_last_adjust_time_get(clock_s* clock)
{
    *clock = clock_data.shift_time;
}
/// @brief 普通校时处理
bool clock_normal_adjust(clock_s* clock)
{
    uint32_t seconds = calendar_to_seconds(&clock->cale);
    uint32_t offset  = labs(seconds - g_clock.u32datetime);

    if(clock_cmp(*clock, g_clock) == 0) return false;             // 时间相同，不处理
    if(is_calendar_validis(&clock->cale) == FALSE) return false;  // 时间无效，不处理
    if(clock_running_para->shift_limit_max && \
       (offset > clock_running_para->shift_limit_max))
    {
        clock_out_stus |= STUS_CLOCK_SHIFT_INVALID;  // 偏差超过最大校时偏差
        return false;  // 偏差超过最大校时偏差，不处理
    }
    else if(clock_running_para->shift_limit_min && \
            (offset < clock_running_para->shift_limit_min))
    {
        return false;  // 偏差小于最小校时偏差，不处理
    }
    if(is_calendar_date_no_equal(&g_clock.cale, &clock->cale))
    {
        clock_data.bc_num = 0;
        clock_data.bc_err_num = 0;
    }
    clock_out_stus |= STUS_CLOCK_SHIFT_EVENT; 
    clock_data.shift_time = g_clock;
    g_clock = *clock;
    clock_data_checkin();
    hw_rtc_set(&g_clock.cale);
    return true;
}

/// @brief 网络时间校时处理
bool clock_network_adjust(clock_s* clock)
{
    uint32_t seconds = calendar_to_seconds(&clock->cale);
    uint32_t offset  = labs(seconds - g_clock.u32datetime);

    if(is_calendar_validis(&clock->cale) == FALSE) return false;  // 时间无效，不处理
    if(offset <= NT_ADJ_LIMIT_MIN || offset > NT_ADJ_LIMIT_MAX || clock->year < NT_ADJ_YEAR_MIN || clock->year >= NT_ADJ_YEAR_MAX) //
    {
        return false;  // 偏差超过最大校时偏差，不处理
    }

    g_clock = *clock;
    clock_data_checkin();
    hw_rtc_set(&g_clock.cale);
    return true;
}

/// @brief 应支持通过密文+MAC 或明文的方式进行广播校时，广播校时应记录事件记录且不应响应
///        时钟偏差在最小校时偏差（默认 1 分钟）内的校时指令。
/// @param  clock `
/// @param  typ  1-密文广播，0-明文广播, 3-07协议
/// @return  
bool clock_bc_time_set(clock_s* clock, uint8_t typ)
{
    uint32_t seconds = calendar_to_seconds(&clock->cale);
    uint32_t offset  = labs(seconds - g_clock.u32datetime);
    bool     ret     = false;

    if(is_calendar_validis(&clock->cale) == FALSE) return false;  // 时间无效，不处理
    if(offset < clock_running_para->bc_limit_min)  return false;  // 时钟偏差小于最小校时偏差，不接受广播校时
    
    if(typ == 0)
    {
        // 密文广播
        clock_out_stus |= STUS_CLOCK_BC_EVENT;  // 广播校时成功
        g_clock = *clock;
        ret = true;
        if(is_calendar_date_no_equal(&g_clock.cale, &clock->cale))
        {
            clock_data.bc_num = 0;
            clock_data.bc_err_num = 0;
        }
    }
    else if(typ == 2)
    {
        if(!clock_data.bc_num)
        {
            clock_out_stus |= STUS_CLOCK_BC_EVENT;  // 广播校时成功
            ret = true;
            g_clock = *clock;
        }
        if(clock_data.bc_num < 100) clock_data.bc_num++;  
    }else
    {
        // 明文广播,当电能表收到明文方式广播校时指令时，如果广播校时的校时范围大于最大校时偏差（默认5分钟），
        // 电能表不接受校时，同时记录时钟故障事件；每个自然日因为该原因最多只生成一条时钟故障事件记录.
        // 通过明文方式进行广播校时时同时需满足，每个自然日只允许执行一次，广播校时校时范围不应大于最大校时
        // 偏差（默认5 分钟），且校时后时间不应跨天和结算日。
        if(is_calendar_date_no_equal(&clock->cale, &g_clock.cale)) return false;  // 时间跨天，不接受广播校时
        if(offset > clock_running_para->bc_limit_max)
        {
            if(!clock_data.bc_err_num)
            {
                clock_out_stus |= STUS_CLOCK_BC_INVALID;  // 广播校时时钟偏差超过最大校时偏差,记录始终故障事件
            }
            if(clock_data.bc_err_num < 100) clock_data.bc_err_num++;
        }
        else
        {
            if(!clock_data.bc_num)
            {
                clock_out_stus |= STUS_CLOCK_BC_EVENT;  // 广播校时成功
                ret = true;
                g_clock = *clock;
            }
            if(clock_data.bc_num < 100) clock_data.bc_num++;
        }
    }

    // DBG_PRINTF(P_645, D, "\r\n clock:   20%02d-%02d-%02d, %02d:%02d:%02d", clock->year, clock->month, clock->day, clock->hour, clock->minute, clock->second);
    // DBG_PRINTF(P_645, D, "\r\n g_clock: 20%02d-%02d-%02d, %02d:%02d:%02d", g_clock.year, g_clock.month, g_clock.day, g_clock.hour, g_clock.minute, g_clock.second);
    clock_data_checkin();
    if(ret == FALSE) return FALSE;
    g_clock.stus.value = 0;
    clock_data.bc_time = g_clock; 
    hw_rtc_set(&g_clock.cale);
    return true;
}

void clock_format_to698(uint8_t* buf, const clock_s* clock)
{
    memcpy(buf, &clock->u32datetime, 6);
}

bool clock_is_valid(const clock_s* clock)
{
    Calendar_s tmp = clock->cale;
    return is_calendar_validis(&tmp);
}

/// @brief 时钟与系统当前时间比较
/// @param clock 
/// @return 0: 时钟时间等于系统时间，1: 时钟时间小于系统时间，-1: 时钟时间大于系统时间
int8_t clock_compare(const clock_s* clock)
{
    return clock_cmp(g_clock, *clock); 
}

/// @brief 比较两个时间的差值
/// @param des 
/// @param src 
/// @return 大于0: des时间大于src时间，等于0: des时间等于src时间，小于0: des时间小于src时间
int32_t clock_diff_value(const clock_s* des, const clock_s* src)
{
    uint32_t des_seconds, src_seconds;

    des_seconds = calendar_to_seconds(&des->cale);
    src_seconds = calendar_to_seconds(&src->cale);

    return (des_seconds - src_seconds);
}

void clock_invalid_set(clock_s* clock)
{
    memset(clock, 0xFF, sizeof(Calendar_s));
    clock->stus.invalid_value = TRUE;
}

void clock_ppm_set(int16_t ppm)
{
    clock_para_checkin(member_offset(ClockPara_s, rtc_ppm), &ppm, 2);
    hal_rtc.ppm_wr(ppm);
}

int16_t clock_ppm_get(void)
{
    return clock_running_para->rtc_ppm;
}

/// @brief 时钟转为BCD格式的时钟, ss mm hh dd mm yy BCD 
/// @param buf 
/// @param clock 
/// @return 
uint8_t clock_format_to645(uint8_t* buf, const clock_s* clock, CLOCK_TYPE typ)
{
    uint8_t *p = buf;
    
    if(typ & 0x02) {*p++ = btobcd(clock->second);}
    if(typ & 0x04) {*p++ = btobcd(clock->minute);}
    if(typ & 0x08) {*p++ = btobcd(clock->hour);}
    if(typ & 0x10) {*p++ = btobcd(clock->week);}
    if(typ & 0x20) {*p++ = btobcd(clock->day); }
    if(typ & 0x40) {*p++ = btobcd(clock->month);}
    if(typ & 0x80) {*p++ = btobcd(clock->year); }
    return (p - buf);
}

/// @brief 秒数转为BCD格式的时钟, ss mm hh dd mm yy BCD 
/// @param buf 
/// @param gseconds 
/// @return 
uint8_t clock_gseconds_to645(uint8_t* buf, uint32_t gseconds, CLOCK_TYPE typ)
{
    uint8_t *p = buf;
    Calendar_s cale;
    seconds_to_calendar(&cale, gseconds);
    cale.date.week = get_week_value(cale.date.year, cale.date.month, cale.date.day);

    if(typ & 0x02) {*p++ = btobcd(cale.time.second);}
    if(typ & 0x04) {*p++ = btobcd(cale.time.minute);}
    if(typ & 0x08) {*p++ = btobcd(cale.time.hour);}
    if(typ & 0x10) {*p++ = btobcd(cale.date.week);}
    if(typ & 0x20) {*p++ = btobcd(cale.date.day);}
    if(typ & 0x40) {*p++ = btobcd(cale.date.month);}
    if(typ & 0x80) {*p++ = btobcd(cale.date.year);}
    return (p - buf);
}

/// @brief ss mm hh dd mm yy BCD 转为 时钟 
/// @param buf 
/// @param clock 
void clock_unformat_frm645(const uint8_t* buf, clock_s* clock, CLOCK_TYPE typ)
{
    uint8_t *p_data = (uint8_t *)buf;

    if(typ & 0x02) {clock->second = bcdtob(*p_data), p_data++;}
    if(typ & 0x04) {clock->minute = bcdtob(*p_data), p_data++;}
    if(typ & 0x08) {clock->hour   = bcdtob(*p_data), p_data++;}
    if(typ & 0x10) {clock->week   = bcdtob(*p_data), p_data++;}
    if(typ & 0x20) {clock->day    = bcdtob(*p_data), p_data++;}
    if(typ & 0x40) {clock->month  = bcdtob(*p_data), p_data++;}
    if(typ & 0x80) {clock->year   = bcdtob(*p_data), p_data++;}
    clock->stus.value = 0x00;
}

/// @brief 时钟调整时区
/// @param clock
/// @param zone 时区偏移量，单位为 1/4小时*100
bool clock_zone_adjust(clock_s* clock, int16_t zone)
{
    uint32_t seconds = calendar_to_seconds(&clock->cale);
    int32_t adj_seconds = ((int32_t)zone * 3600)/4; // 时区转换为秒
    
    if(adj_seconds > 43200 || adj_seconds < -43200) // 时区范围[-12, 12]小时
    {
        return false; // 时区超出范围
    }

    seconds = (adj_seconds < 0) ? (seconds - labs(adj_seconds)) : (seconds + adj_seconds);

    seconds_to_calendar(&clock->cale, seconds);
    return true;
}

const struct mclock_s mclock = 
{
    .datetime            = &g_clock,
    .init                = clock_init,
    .refresh             = clock_update,
    .pwr_down_save       = clock_pwr_down_save,
    .reset               = clock_reset,
    .state_query         = clock_state_query,
    .state_clr           = clock_state_clr,
    .para_get            = clock_para_get,
    .para_set            = clock_para_set,
    .pdtime_get          = clock_pdtime_get,
    .sync_time_get       = clock_last_adjust_time_get,
    .sync_time_set       = clock_normal_adjust,
    .network_time_set    = clock_network_adjust,
    .bc_time_set         = clock_bc_time_set,
    .is_valid            = clock_is_valid,
    .compare             = clock_compare,  
    .invalid_set         = clock_invalid_set,
    .ppm_set             = clock_ppm_set,
    .ppm_get             = clock_ppm_get,
    .seconds_to_calendar = seconds_to_calendar,
    .calendar_to_seconds = calendar_to_seconds,
    .format_to645        = clock_format_to645,
    .unformat_frm645     = clock_unformat_frm645,
    .gseconds_to645      = clock_gseconds_to645,
    .diff_value          = clock_diff_value,
    .get_month_days      = get_month_days,
    .get_week_value      = get_week_value,
    .zone_adjust         = clock_zone_adjust,
};





