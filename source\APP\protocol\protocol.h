/******************************************************************************
 *    (c) Copyright 2016, SheWei Electronics Co.,Ltd.
 *    All Rights Reserved
 *
 *    Filename:      protocol.h
 *    Describe:
 *
 *    Device:
 *    Compiler:
 *
 *    Created on:
 *    Modify record:
 *
 *******************************************************************************/
#ifndef __PROTOCOL_H__
#define __PROTOCOL_H__

/// 选择默认的通讯协议
#define PROTOCOL_DEFAULT_TYPE PROTOCOL_TYPE_DLT645    /// 默认的通讯协议类型
#define PROTOCOL_TIME_OUT_CNT 30                      /// 协议超时时间，单位秒
#define PROTOCOL_BUFF_MAX 512                         /// 协议数据缓存最大长度

#define PROTOCOL_645_DES_ENCRYPT 1    /// 是否启用DES加密，1-启用，0-不启用

typedef enum
{
    PROTOCOL_TYPE_NULL,
    PROTOCOL_TYPE_DLT645,
    PROTOCOL_TYPE_DLT698,
} PROTOCOL_TYPE_t;

#endif /* __PROTOCOL_H__ */
