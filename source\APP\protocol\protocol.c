/******************************************************************************
*    (c) Copyright 2016, SheWei Electronics Co.,Ltd.
*    All Rights Reserved
*
*    Filename:      protocol.c
*    Describe:      
*
*    Device:
*    Compiler:
*
*    Created on:
*    Modify record:
*
*******************************************************************************/
#include "app.h"
#include "comm_phy.h"
#include "dcu.h"
#include "protocol.h"
#include "local_port.h"
#include "debug.h"
#include "DLT645_2007.h"


typedef struct
{
    PROTOCOL_TYPE_t protocol_type;
    uint8_t buff[PROTOCOL_BUFF_MAX];
}protocol_ctrl_s;

static protocol_ctrl_s p_ctrl[PHY_CHN_NUM];
static uint16_t protocol_time_out_cnt[PHY_CHN_NUM];

/// @brief 通讯协议初始化
/// @param  
void protocol_init(void)
{
    uint8_t i;
#if HW_DCU_MODUL
    dcu.init();
#endif
    for(i = 0; i < PHY_CHN_NUM; i++)
    {
        p_ctrl[i].protocol_type = PROTOCOL_DEFAULT_TYPE;
        /// 端口数据初始化，获取默认波特等信息
        local_port.init(i);
        /// 物理通道初始化
        comm_phy.init(i, p_ctrl[i].buff, PROTOCOL_BUFF_MAX, local_port.baud_rate_get(i));
        /// 协议初始化
        DLT645.init(i, p_ctrl[i].buff);
    }
}
/// @brief 通讯协议运行，放入主循环中
/// @param  
void protocol_running(void)
{
    uint16_t msg_len;
    uint16_t send_len;

#if HW_DCU_MODUL
    dcu.process();  // 处理DCU通道
#endif
    for(uint8_t i = 0; i < PHY_CHN_NUM; i++)
    {
        msg_len = comm_phy.recv(i);
        if(msg_len > 0)
        {
            DBG_PRINTF(P_PROT, T);
            DBG_PRINTF(P_PROT, D, " -- local chn:%d, recv:%d", i, msg_len);
            DBG_PRINTF(P_PROT, M, p_ctrl[i].buff, msg_len);
            uint8_t ack = ACK_NULL;
            if((p_ctrl[i].protocol_type == PROTOCOL_TYPE_DLT698) || \
               (p_ctrl[i].protocol_type == PROTOCOL_TYPE_NULL))
            {
                send_len = 0; // 添加698协议后删除这里
                // send_len = dlg698.running(i, &ack, msg_len);
                if(send_len)
                {
                    p_ctrl[i].protocol_type = PROTOCOL_TYPE_DLT698;
                    comm_phy.send(i, p_ctrl[i].buff, send_len);
                    protocol_time_out_cnt[i] = PROTOCOL_TIME_OUT_CNT;
                    DBG_PRINTF(P_PROT, T);
                    DBG_PRINTF(P_PROT, D, " -- local chn:%d, send:%d", i, send_len);
                    DBG_PRINTF(P_PROT, M, p_ctrl[i].buff, send_len);
                    ///断开请求？

                }
                ///

                // if(ack != ACK_NULL) continue;
            }

            if((p_ctrl[i].protocol_type == PROTOCOL_TYPE_DLT645) || \
               ((p_ctrl[i].protocol_type == PROTOCOL_TYPE_NULL) && (ack == ACK_NULL)))
            {
                send_len = DLT645.msg_process(i, &ack, msg_len);
                if(send_len)
                {
                    p_ctrl[i].protocol_type = PROTOCOL_TYPE_DLT645;
                    comm_phy.send(i, p_ctrl[i].buff, send_len);
                    protocol_time_out_cnt[i] = 5;   ///启用698协议后这里改为 1
                    DBG_PRINTF(P_PROT, T);
                    DBG_PRINTF(P_PROT, D, " -- local chn:%d, send:%d", i, send_len);
                    DBG_PRINTF(P_PROT, M, p_ctrl[i].buff, send_len);
                    continue;
                }
            }
        }
    }
}
/// @brief 通讯协议定时器运行，每秒调用一次
/// @param  
void protocol_second_running(void)
{
    for(uint8_t i = 0; i < PHY_CHN_NUM; i++)
    {
        if(protocol_time_out_cnt[i] > 0)
        {
            protocol_time_out_cnt[i]--;
            if(protocol_time_out_cnt[i] == 0)
            {
                p_ctrl[i].protocol_type = PROTOCOL_TYPE_NULL;
                DBG_PRINTF(P_PROT, D, "\r\n local chn:%d, protocol time out", i);
            }
        }
    }
}


const struct app_task_t protocol_task = 
{
    .init       = protocol_init,
    .idle_run   = protocol_running,
    .second_run = protocol_second_running,
};

