/**
 ******************************************************************************
* @file    relay.c
* <AUTHOR> @date    2024
* @brief   继电器驱动.
*
******************************************************************************
*
* @note
* Copyright (C) 2024  SheWei Electrics Co.,Ltd. All rights reserved.
*
*
******************************************************************************/
/* Includes ------------------------------------------------------------------*/
#include "relay.h"

/* Private typedef -----------------------------------------------------------*/
typedef struct
{
    uint8_t     level_cnt;       // 检测到电平计数器
    uint8_t     smaple_period;   // 采样周期, 单位ms
    RLY_STATE_t state;           // 状态标志
} rly_chk_s;

typedef struct
{
    uint16_t  act_timer;           // 动作脉宽计时器
    uint16_t  lvd_restore_timer;   // 掉电检测恢复计时器
#if USE_RLY_CHK || USE_RLY_CHK_EX
    rly_chk_s chk;
#endif
} rly_con_s;

/* Private define ------------------------------------------------------------*/
#define RLY_CTRL_DUTY           80         // 继电器控制正脉宽宽度 Unit: ms,单稳态的设置为0，双稳态的设置为80
#define RLY_AUXI_DUTY           0          // 辅助继电器控制周期,  Unit: ms,单稳态的设置为0，双稳态的设置为80

#define RLY_CHK_PERIOD          40         // 继电器合断检测周期, !!必须至少3个交流半波以上, Unit: ms

/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
static rly_con_s rly_con[RLY_NUM];


static const uint8_t rly_ctrl_duty[RLY_NUM] = 
{
 #if RLY_INSIDE_ENABLE
    RLY_CTRL_DUTY,
#endif
#if RLY_OUTSIDE_ENABLE
    RLY_AUXI_DUTY,
#endif
};

/* Private functions ---------------------------------------------------------*/

/// @brief 继电器闭合动作，根据type选择相应的继电器
/// @param type 
static void act_switch_on(RLY_TYPE_t type)
{
    switch(type)
    {
    #if RLY_INSIDE_ENABLE
        case TYPE_RLY_1:
        RLY_1_OPEN_CLR();
        RLY_1_CLOSE_SET();
        break;
    #endif

    #if RLY_OUTSIDE_ENABLE
        case TYPE_RLY_2:
        RLY_2_OPEN_CLR();
        RLY_2_CLOSE_SET();
        break;
    #endif


    }
}

/// @brief 继电器断开动作，根据type选择相应的继电器
/// @param type 
static void act_switch_off(RLY_TYPE_t type)
{
    switch(type)
    {
    #if RLY_INSIDE_ENABLE
        case TYPE_RLY_1:
        RLY_1_CLOSE_CLR();
        RLY_1_OPEN_SET();
        break;
    #endif

    #if RLY_OUTSIDE_ENABLE
        case TYPE_RLY_2:
        RLY_2_CLOSE_CLR();
        RLY_2_OPEN_SET();
        break;
    #endif
    }
}

/// @brief 继电器复位，根据type选择相应的继电器
/// @param type 
static void act_reset(RLY_TYPE_t type)
{
    switch(type)
    {
    #if RLY_INSIDE_ENABLE
        case TYPE_RLY_1:
        RLY_1_CLOSE_CLR();
        RLY_1_OPEN_CLR();
        break;
    #endif

    #if RLY_OUTSIDE_ENABLE
        case TYPE_RLY_2:
        RLY_2_CLOSE_CLR();
        RLY_2_OPEN_CLR();
        break;
    #endif
    }
}

#if USE_RLY_CHK || USE_RLY_CHK_EX
static bool act_detect(RLY_TYPE_t type)
{
    switch(type)
    {
    #if RLY_INSIDE_ENABLE
        case TYPE_RLY_1:
        return IS_RLY_1_CLOSE_ON();
    #endif

    #if RLY_OUTSIDE_ENABLE
        case TYPE_RLY_2:
        return IS_RLY_2_CLOSE_ON();
    #endif
    }
    return FALSE;
}
#endif

/// @brief 拉合闸计数扫描，在1ms系统中断进程中执行
/// @param  
SYSTICKCALL(bsp_relay_scan)
{
    rly_con_s *con = &rly_con[0];
    for(uint16_t i = 0; i < RLY_NUM; i++, con++)
    {
        if(con->act_timer > 0 && --con->act_timer == 0)
        {
            act_reset((RLY_TYPE_t)i);
        }

    #if RLY_INSIDE_ENABLE   ///只有内置继电器拉合闸时才需要禁止掉电检测
        if(con->lvd_restore_timer > 0 && --con->lvd_restore_timer == 0)
        {
            hal_mcu.stus->rly_act = false; ///恢复上下电检测
        }
    #endif
    }
#if (USE_RLY_CHK && RLY_INSIDE_ENABLE)
    con = &rly_con[TYPE_RLY_1];
    if(con->chk.smaple_period > 0)
    {
        if(act_detect(TYPE_RLY_1)) con->chk.level_cnt++; // 每检测到有效电平+1次
        con->chk.smaple_period--;
    }
    else
    {
        con->chk.state = (con->chk.level_cnt >= (RLY_CHK_PERIOD * 0.85)) ? STA_RLY_ON : STA_RLY_OFF;
        con->chk.level_cnt = 0;
        con->chk.smaple_period = RLY_CHK_PERIOD;
    }
#endif
#if (USE_RLY_CHK_EX && RLY_OUTSIDE_ENABLE)
    con = &rly_con[TYPE_RLY_2];
    if(con->chk.smaple_period > 0)
    {
        if(act_detect(TYPE_RLY_2)) con->chk.level_cnt++; // 每检测到有效电平+1次
        con->chk.smaple_period--;
    }
    else
    {
        con->chk.state = (con->chk.level_cnt >= (RLY_CHK_PERIOD * 0.85)) ? STA_RLY_ON : STA_RLY_OFF;
        con->chk.level_cnt = 0;
        con->chk.smaple_period = RLY_CHK_PERIOD;
    }
#endif
}

/// @brief 继电器初始化
/// @param  
void bsp_relay_init(void)
{
    hal_timer.systick_insert(&bsp_relay_scan);
}

/// @brief 读取继电器状态
/// @param type 继电器类型
/// @return 继电器状态
RLY_STATE_t bsp_rly_status_get(RLY_TYPE_t type)
{
#if USE_RLY_CHK || USE_RLY_CHK_EX
    if(type >= RLY_NUM) return STA_RLY_UNASSURED;
    return rly_con[type].chk.state;
#else
    return STA_RLY_UNASSURED;
#endif
}

/// @brief 设置继电器动作模式
/// @param type 继电器类型
/// @param mode 继电器动作模式
void bsp_rly_act_cmd(RLY_TYPE_t type, RLY_MODE_t mode)
{
#if RLY_INSIDE_ENABLE   ///只有双稳态继电器才需要等待
    while(rly_con[type].act_timer != 0) { hal_mcu.wait_us(1000); } /// 等待上次动作稳定
    act_reset(type);
#endif
    if(mode == MODE_RLY_ON)
    {
        act_switch_on(type);
    }
    else
    {
        act_switch_off(type);
    }
#if RLY_INSIDE_ENABLE   ///只有内置继电器拉合闸时才需要禁止掉电检测
    rly_con[type].act_timer = rly_ctrl_duty[type];          /// 动作脉宽设定
    if(type == TYPE_RLY_1)
    {
        hal_mcu.stus->rly_act   = true;                   /// 置位继电器动作标志，通知MCU暂停上下电检测
        rly_con[type].lvd_restore_timer = RLY_CTRL_DUTY + 80;  /// 动作脉宽设定
    }
#endif
}

#if RLY_ALARM_ENABLE
/// @brief 继电器报警动作
/// @param mode 继电器报警模式
void bsp_rly_alarm_cmd(RLY_MODE_t mode)
{
    if(mode == MODE_RLY_ON)
    {
        RLY_3_OPEN_CLR();
        RLY_3_CLOSE_SET();
    }
    else
    {
        RLY_3_CLOSE_CLR();
        RLY_3_OPEN_SET();
    }
}
#endif

/// @brief 声明relay子模块对象
const struct relay_t relay =
{
    .init                = bsp_relay_init,
    .status              = bsp_rly_status_get,
    .action              = bsp_rly_act_cmd,
#if RLY_ALARM_ENABLE
    .alarm               = bsp_rly_alarm_cmd,
#endif
};
