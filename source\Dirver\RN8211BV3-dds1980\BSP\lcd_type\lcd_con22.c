/**
 ******************************************************************************
 * @file    lcd_jsh209105y.c
 * <AUTHOR> @date    2024
 * @brief   lcd_jsh209105y 显示屏定义
 * @note
 *
 ******************************************************************************
 *
 * @note
 * Copyright (C) 2024 SheWei Electrics Co.,Ltd. All rights reserved.
 *
 *
 ******************************************************************************/

/* Includes ------------------------------------------------------------------*/
#include "typedef.h"
#if USE_LCD_EXT_DRIVER
#include "ext_lcd_driver.h"
#else
#include "hal_lcd.h"
#endif
/* 定义LCD主屏, 辅屏数显个数 */
#define LCD_MS_DIGITS 8    /// 定义主屏第一行数显个数
#define LCD_LS_DIGITS 0    /// 定义主屏未尾小8的个数
#define LCD_PS_DIGITS 0    /// 定义辅屏8的个数

#define LCD_MS_MINUS_SIGN 0    /// 定义主屏前是否有单独的负号

/* 定义LCD COM0~8的SEG */
/*****************************************
 *
 * 统一格式
 * SEG_XY   数码8， X-序号，Y-8段 如：第一个数的G段-SEG_1G
 *          符号，  参考真值表定义 // X-固定为S，Y-按照液晶符号序号
 *          小数点，X-序号，Y-8段 如：第一个数的小数点-SEG_1H  // X-固定为DP，Y-按照液晶小数点序号
 *
 ******************************************/
/// @brief LCD的SEG1
#define SEG_2A COM3(LCD_SEG01)
#define SEG_2F COM2(LCD_SEG01)
#define SEG_2E COM1(LCD_SEG01)
#define SEG_2D COM0(LCD_SEG01)

#define SEG_S1_TOTAL COM7(LCD_SEG02)
#define SEG_2B COM6(LCD_SEG02)
#define SEG_2G COM5(LCD_SEG02)
#define SEG_2C COM4(LCD_SEG02)

#define SEG_3A COM3(LCD_SEG03)
#define SEG_3F COM2(LCD_SEG03)
#define SEG_3E COM1(LCD_SEG03)
#define SEG_3D COM0(LCD_SEG03)

#define SEG_S2_TARIFF COM7(LCD_SEG04)
#define SEG_3B COM6(LCD_SEG04)
#define SEG_3G COM5(LCD_SEG04)
#define SEG_3C COM4(LCD_SEG04)

#define SEG_4A COM3(LCD_SEG05)
#define SEG_4F COM2(LCD_SEG05)
#define SEG_4E COM1(LCD_SEG05)
#define SEG_4D COM0(LCD_SEG05)

#define SEG_4B COM7(LCD_SEG06)
#define SEG_4G COM6(LCD_SEG06)
#define SEG_4C COM5(LCD_SEG06)
#define SEG_S9_P_DOWN COM4(LCD_SEG06)

#define SEG_1A COM7(LCD_SEG07)
#define SEG_1F COM6(LCD_SEG07)
#define SEG_1E COM5(LCD_SEG07)
#define SEG_1D COM4(LCD_SEG07)

#define SEG_S3_REMAINING COM3(LCD_SEG08)
#define SEG_1B COM2(LCD_SEG08)
#define SEG_1G COM1(LCD_SEG08)
#define SEG_1C COM0(LCD_SEG08)

#define SEG_5A COM7(LCD_SEG09)
#define SEG_5F COM6(LCD_SEG09)
#define SEG_5E COM5(LCD_SEG09)
#define SEG_5D COM4(LCD_SEG09)

#define SEG_5B COM3(LCD_SEG10)
#define SEG_5G COM2(LCD_SEG10)
#define SEG_5C COM1(LCD_SEG10)
#define SEG_S7_P_1 COM0(LCD_SEG10)

#define SEG_6A COM7(LCD_SEG11)
#define SEG_6F COM6(LCD_SEG11)
#define SEG_6E COM5(LCD_SEG11)
#define SEG_6D COM4(LCD_SEG11)

#define SEG_S4_RELAY_OFF COM3(LCD_SEG12)
#define SEG_6B COM2(LCD_SEG12)
#define SEG_6G COM1(LCD_SEG12)
#define SEG_6C COM0(LCD_SEG12)

#define SEG_7A COM7(LCD_SEG13)
#define SEG_7F COM6(LCD_SEG13)
#define SEG_7E COM5(LCD_SEG13)
#define SEG_7D COM4(LCD_SEG13)

#define SEG_7B COM3(LCD_SEG14)
#define SEG_7G COM2(LCD_SEG14)
#define SEG_7C COM1(LCD_SEG14)
#define SEG_S8_P_UP COM0(LCD_SEG14)

#define SEG_8A COM7(LCD_SEG15)
#define SEG_8F COM6(LCD_SEG15)
#define SEG_8E COM5(LCD_SEG15)
#define SEG_8D COM4(LCD_SEG15)

#define SEG_S5_KW COM3(LCD_SEG16)
#define SEG_8B COM2(LCD_SEG16)
#define SEG_8G COM1(LCD_SEG16)
#define SEG_8C COM0(LCD_SEG16)

#define SEG_9A COM7(LCD_SEG17)
#define SEG_9F COM6(LCD_SEG17)
#define SEG_9E COM5(LCD_SEG17)
#define SEG_9D COM4(LCD_SEG17)

#define SEG_S6_H COM3(LCD_SEG18)
#define SEG_9B COM2(LCD_SEG18)
#define SEG_9G COM1(LCD_SEG18)
#define SEG_9C COM0(LCD_SEG18)

#define SEG_NULL 0

#define I_A 0x0001
#define I_B 0x0002
#define I_C 0x0004
#define I_D 0x0008
#define I_E 0x0010
#define I_F 0x0020
#define I_G 0x0040
#define I_I 0x0080

/*  lcd digit and char macro define */
#define SPACE 0    // all the SEGments of the char off
#define CHAR_0 (I_A | I_B | I_C | I_D | I_E | I_F)
#define CHAR_1 (I_B | I_C)
#define CHAR_2 (I_A | I_B | I_D | I_E | I_G)
#define CHAR_3 (I_A | I_B | I_C | I_D | I_G)
#define CHAR_4 (I_B | I_C | I_F | I_G)
#define CHAR_5 (I_A | I_C | I_D | I_F | I_G)
#define CHAR_6 (I_A | I_C | I_D | I_E | I_F | I_G)
#define CHAR_7 (I_A | I_B | I_C)
#define CHAR_8 (I_A | I_B | I_C | I_D | I_E | I_F | I_G)
#define CHAR_9 (I_A | I_B | I_C | I_D | I_F | I_G)
#define CHAR_A (I_A | I_B | I_C | I_E | I_F | I_G)    // 'A'
#define CHAR_b (I_C | I_D | I_E | I_F | I_G)          // 'b'
#define CHAR_C (I_A | I_D | I_E | I_F)                // 'C'
#define CHAR_d (I_B | I_C | I_D | I_E | I_G)          // 'd'
#define CHAR_E (I_A | I_D | I_E | I_F | I_G)          // 'E'
#define CHAR_F (I_A | I_E | I_F | I_G)                // 'F'
#define CHAR_G (I_A | I_C | I_D | I_E | I_F)          // 'G'
#define CHAR_H (I_C | I_E | I_F | I_G)                // 'h'
#define CHAR_I (I_E | I_F)                            // 'I'
#define CHAR_J (I_B | I_C | I_D | I_E)                // 'J'
#define CHAR_k (I_E | I_F | I_G)                      // 'k'
#define CHAR_L (I_D | I_E | I_F)                      // 'L'
#define CHAR_m (I_A | I_C | I_E)                      // 'm'
#define CHAR_N (I_A | I_B | I_C | I_E | I_F)          // 'N'
#define CHAR_O (I_A | I_B | I_C | I_D | I_E | I_F)    // 'O'
#define CHAR_P (I_A | I_B | I_E | I_F | I_G)          // 'P'
#define CHAR_q (I_A | I_B | I_C | I_F | I_G)          // 'q'
#define CHAR_r (I_E | I_G)                            // 'r'
#define CHAR_S (I_A | I_C | I_D | I_F | I_G)          // 'S'
#define CHAR_t (I_D | I_E | I_F | I_G)                // 't'
#define CHAR_u (I_B | I_C | I_D | I_E | I_F)          // 'u'
#define CHAR_V (I_C | I_D | I_E)                      // 'V'
#define CHAR_w (I_B | I_D | I_F)                      // 'w'
#define CHAR_x (I_B | I_C | I_E | I_F | I_G)          // 'x'
#define CHAR_y (I_B | I_C | I_D | I_F | I_G)          // 'y'
#define CHAR_Z (I_A | I_B | I_D | I_E | I_G)          // 'Z'
#define CHAR_c (I_D | I_E | I_G)                      // 'c'
#define CHAR_n (I_C | I_E | I_G)                      // 'n'
#define CHAR_o (I_C | I_D | I_E | I_G)                // 'o'
#define CHAR_ (I_G)                                   // '-'  短杠
#define CHAR__ (I_D)                                  // '_'  下划线

/* Private typedef -----------------------------------------------------------*/
typedef uint16_t SEG_TYPE_t;    ///
typedef struct
{
    const SEG_TYPE_t *seg_tab;
    const SEG_TYPE_t *dot_tab;
    uint8_t           seg_num;
    uint8_t           dot_num;
    uint8_t           digit_num;
} Screen_s;

/// @brief 主屏数字段址表, 从左到右，从上到下
static const SEG_TYPE_t ms_digit_segs[][7] = {
    // 数据显示
    {SEG_2A, SEG_2B, SEG_2C, SEG_2D, SEG_2E, SEG_2F, SEG_2G}, {SEG_3A, SEG_3B, SEG_3C, SEG_3D, SEG_3E, SEG_3F, SEG_3G}, {SEG_4A, SEG_4B, SEG_4C, SEG_4D, SEG_4E, SEG_4F, SEG_4G},
    {SEG_5A, SEG_5B, SEG_5C, SEG_5D, SEG_5E, SEG_5F, SEG_5G}, {SEG_6A, SEG_6B, SEG_6C, SEG_6D, SEG_6E, SEG_6F, SEG_6G}, {SEG_7A, SEG_7B, SEG_7C, SEG_7D, SEG_7E, SEG_7F, SEG_7G},
    {SEG_8A, SEG_8B, SEG_8C, SEG_8D, SEG_8E, SEG_8F, SEG_8G}, {SEG_9A, SEG_9B, SEG_9C, SEG_9D, SEG_9E, SEG_9F, SEG_9G},
};
/// @brief 主屏小数点段址表
static const SEG_TYPE_t ms_dot_segs[] = {SEG_NULL, SEG_NULL, SEG_NULL, SEG_S7_P_1, SEG_S8_P_UP, SEG_S9_P_DOWN};

#if LCD_PS_DIGITS != 0
/// @brief 副屏数字段址表, 从左到右
static const SEG_TYPE_t ps_digit_segs[][7] = {
    // ID 数据标识
    {SEG_1A, SEG_1B, SEG_1C, SEG_1D, SEG_1E, SEG_1F, SEG_1G}, {SEG_2A, SEG_2B, SEG_2C, SEG_2D, SEG_2E, SEG_2F, SEG_2G}, {SEG_3A, SEG_3B, SEG_3C, SEG_3D, SEG_3E, SEG_3F, SEG_3G},
    {SEG_4A, SEG_4B, SEG_4C, SEG_4D, SEG_4E, SEG_4F, SEG_4G}, {SEG_5A, SEG_5B, SEG_5C, SEG_5D, SEG_5E, SEG_5F, SEG_5G},
};

/// @brief 副屏小数点段址表
static const SEG_TYPE_t ps_dot_segs[] = {SEG_1H, SEG_2H, SEG_3H, SEG_4H};
#endif

#if LCD_P1S_DIGITS != 0
/// @brief 主屏数字段址表, 从左到右，从上到下
static const SEG_TYPE_t p1s_digit_segs[][7] = {
    // 数据显示
    {SEG_6A, SEG_6B, SEG_6C, SEG_6D, SEG_6E, SEG_6F, SEG_6G},        {SEG_7A, SEG_7B, SEG_7C, SEG_7D, SEG_7E, SEG_7F, SEG_7G},
    {SEG_8A, SEG_8B, SEG_8C, SEG_8D, SEG_8E, SEG_8F, SEG_8G},        {SEG_9A, SEG_9B, SEG_9C, SEG_9D, SEG_9E, SEG_9F, SEG_9G},
    {SEG_10A, SEG_10B, SEG_10C, SEG_10D, SEG_10E, SEG_10F, SEG_10G},
};
/// @brief 主屏小数点段址表
static const SEG_TYPE_t p1s_dot_segs[] = {SEG_1H, SEG_2H, SEG_3H, SEG_4H};
#endif

#if LCD_P2S_DIGITS != 0
/// @brief 主屏数字段址表, 从左到右，从上到下
static const SEG_TYPE_t p2s_digit_segs[][7] = {
    // 数据显示
    {SEG_11A, SEG_11B, SEG_11C, SEG_11D, SEG_11E, SEG_11F, SEG_11G}, {SEG_12A, SEG_12B, SEG_12C, SEG_12D, SEG_12E, SEG_12F, SEG_12G},
    {SEG_13A, SEG_13B, SEG_13C, SEG_13D, SEG_13E, SEG_13F, SEG_13G}, {SEG_14A, SEG_14B, SEG_14C, SEG_14D, SEG_14E, SEG_14F, SEG_14G},
    {SEG_15A, SEG_15B, SEG_15C, SEG_15D, SEG_15E, SEG_15F, SEG_15G},
};

/// @brief 主屏小数点段址表
static const SEG_TYPE_t p2s_dot_segs[] = {SEG_1H, SEG_2H, SEG_3H, SEG_4H};
#endif

static const Screen_s digit_screen[] =    // 显示屏的参数
    {
        {
            .seg_tab   = ms_digit_segs[0],
            .seg_num   = sizeof(ms_digit_segs[0]) / sizeof(SEG_TYPE_t),
            .digit_num = eleof(ms_digit_segs),
            .dot_tab   = ms_dot_segs,
            .dot_num   = eleof(ms_dot_segs),
        },
#if LCD_PS_DIGITS != 0
        {
            .seg_tab   = ps_digit_segs[0],
            .seg_num   = sizeof(ps_digit_segs[0]) / sizeof(SEG_TYPE_t),
            .digit_num = eleof(ps_digit_segs),
            .dot_tab   = ps_dot_segs,
            .dot_num   = eleof(ps_dot_segs),
        },
#endif
#if LCD_P1S_DIGITS != 0
        {
            .seg_tab   = p1s_digit_segs[0],
            .seg_num   = sizeof(p1s_digit_segs[0]) / sizeof(SEG_TYPE_t),
            .digit_num = eleof(p1s_digit_segs),
            .dot_tab   = p1s_dot_segs,
            .dot_num   = eleof(p1s_dot_segs),
        },
#endif
#if LCD_P2S_DIGITS != 0
        {
            .seg_tab   = p2s_digit_segs[0],
            .seg_num   = sizeof(p2s_digit_segs[0]) / sizeof(SEG_TYPE_t),
            .digit_num = eleof(p2s_digit_segs),
            .dot_tab   = p2s_dot_segs,
            .dot_num   = eleof(p2s_dot_segs),
        },
#endif
};

static const uint8_t screen_number = eleof(digit_screen);
/// @brief 单位
static const SEG_TYPE_t unit_none[] = {SEG_NULL};
static const SEG_TYPE_t unit_time[] = {SEG_S7_P_1, SEG_S8_P_UP, SEG_S9_P_DOWN};
static const SEG_TYPE_t unit_date[] = {SEG_S7_P_1, SEG_S8_P_UP, SEG_S9_P_DOWN};
static const SEG_TYPE_t unit_sign[] = {SEG_2G};    // 负号

static const SEG_TYPE_t unit_kWh[]   = {SEG_S5_KW,SEG_S6_H, SEG_2A, SEG_2F, SEG_2G, SEG_2E, SEG_2D};
static const SEG_TYPE_t unit_kVAh[]  = {SEG_NULL};
static const SEG_TYPE_t unit_kvarh[] = {SEG_NULL};

static const SEG_TYPE_t unit_kW[]   = {SEG_S5_KW ,SEG_2A, SEG_2B, SEG_2F, SEG_2G, SEG_2E};
static const SEG_TYPE_t unit_kVA[]  = {SEG_2A, SEG_2B, SEG_2F, SEG_2G, SEG_2C};
static const SEG_TYPE_t unit_kvar[] = {SEG_2A, SEG_2F, SEG_2G, SEG_2C, SEG_2D};

static const SEG_TYPE_t unit_V[]  = {SEG_2F, SEG_2E, SEG_2D, SEG_2C, SEG_2B};
static const SEG_TYPE_t unit_A[]  = {SEG_2A, SEG_2F, SEG_2G, SEG_2B, SEG_2E, SEG_2C};
static const SEG_TYPE_t unit_Hz[] = {SEG_2A, SEG_2F, SEG_2G, SEG_2E};

/// @brief 图标
static const SEG_TYPE_t icon_total[]     = {SEG_S1_TOTAL};
static const SEG_TYPE_t icon_tariff[]    = {SEG_S2_TARIFF};
static const SEG_TYPE_t icon_remaining[] = {SEG_S3_REMAINING};
static const SEG_TYPE_t icon_relay_off[] = {SEG_S4_RELAY_OFF};

/// @brief  费率字体加数字显示
static const SEG_TYPE_t icon_tariff_1[] = {SEG_S2_TARIFF, SEG_1B, SEG_1C};                                            // 费率1
static const SEG_TYPE_t icon_tariff_2[] = {SEG_S2_TARIFF, SEG_1A, SEG_1B, SEG_1D, SEG_1E, SEG_1G};                    // 费率2
static const SEG_TYPE_t icon_tariff_3[] = {SEG_S2_TARIFF, SEG_1A, SEG_1B, SEG_1C, SEG_1D, SEG_1G};                    // 费率3
static const SEG_TYPE_t icon_tariff_4[] = {SEG_S2_TARIFF, SEG_1B, SEG_1C, SEG_1F, SEG_1G};                            // 费率4
static const SEG_TYPE_t icon_tariff_5[] = {SEG_S2_TARIFF, SEG_1A, SEG_1C, SEG_1D, SEG_1F, SEG_1G};                    // 费率5
static const SEG_TYPE_t icon_tariff_6[] = {SEG_S2_TARIFF, SEG_1A, SEG_1C, SEG_1D, SEG_1E, SEG_1F, SEG_1G};            // 费率6
static const SEG_TYPE_t icon_tariff_7[] = {SEG_S2_TARIFF, SEG_1A, SEG_1B, SEG_1C};                                    // 费率7
static const SEG_TYPE_t icon_tariff_8[] = {SEG_S2_TARIFF, SEG_1A, SEG_1B, SEG_1C, SEG_1D, SEG_1E, SEG_1F, SEG_1G};    // 费率8
static const SEG_TYPE_t icon_tariff_9[] = {SEG_S2_TARIFF, SEG_1A, SEG_1B, SEG_1C, SEG_1D, SEG_1F, SEG_1G};            // 费率9

/// @brief 数字字段码表
static const uint8_t segs_digit_tab[] = {
    CHAR_0, CHAR_1, CHAR_2, CHAR_3, CHAR_4, CHAR_5, CHAR_6, CHAR_7, CHAR_8, CHAR_9,
};

/// @brief 字母字段码表
static const uint8_t segs_alp_tab[] = {
    CHAR_A, CHAR_b, CHAR_C, CHAR_d, CHAR_E, CHAR_F, CHAR_G, CHAR_H, CHAR_I, CHAR_J, CHAR_k, CHAR_L, CHAR_m,
    CHAR_N, CHAR_o, CHAR_P, CHAR_q, CHAR_r, CHAR_S, CHAR_t, CHAR_u, CHAR_V, CHAR_w, CHAR_x, CHAR_y, CHAR_Z,
};
