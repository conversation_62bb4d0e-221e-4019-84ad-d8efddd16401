/**
 ******************************************************************************
* @file    hal_flash.c
* <AUTHOR> @date    2024
* @brief   mcu flash driver
*
******************************************************************************
*
* @note
* Copyright (C) 2024  SheWei Electrics Co.,Ltd. All rights reserved.
*
*
******************************************************************************/

/* Includes ------------------------------------------------------------------*/
#include <string.h>
#include "hal_flash.h"
#include "boot_cfg.h"
#include "rn8xxx_ll_flash.h"

/* Private macro -------------------------------------------------------------*/
#define IS_DATA_ADDR(x,n)  (x >= MCU_INFO_ADDR && x < MCU_INFO_ADDR + MCU_INFO_SIZE)&& \
                            ((x + n) < MCU_INFO_ADDR + MCU_INFO_SIZE)

#define IS_CODE_ADDR(x,n)  (x >= MCU_FLASH_APP_BASE && x < MCU_FLASH_APP_BASE + MCU_FLASH_APP_SIZE && \
                            (x + n) < MCU_FLASH_APP_BASE + MCU_FLASH_APP_SIZE)


///@页擦除
static void mcu_page_erase(HAL_FLASH_ADDR_t ofst)
{
    // en_flash_lock_t FlashLockNum;             // 定义Flash锁定编号
    // uint32_t LockValue = 0;                   // 锁定值
    // uint8_t para = 0;                              // 额外参数
    // Flash_OpModeConfig(FlashSectorEraseMode); // 进入擦除模式
    // // 根据地址确定锁定区域
    // switch ((ofst / 1024) / 64)
    // {
    // case 0:
    //     FlashLockNum = FlashLock0;
    //     para = ((((ofst / 1024) - 0) / 2));
    //     break;
    // case 1:
    //     FlashLockNum = FlashLock1;
    //     para = ((((ofst / 1024) - 64) / 2));
    //     break;
    // case 2:
    //     FlashLockNum = FlashLock2;
    //     para = ((((ofst / 1024) - 128) / 2));
    //     break;
    // case 3:
    //     FlashLockNum = FlashLock3;
    //     para = ((((ofst / 1024) - 192) / 2));
    //     break;
    // }
    // LockValue |= (1 << para);               // 设置锁定位
    // Flash_LockSet(FlashLockNum, LockValue); // 设置锁定
    // Flash_SectorErase(ofst);                // 擦除指定扇区
    // Flash_LockAll();                        // 加锁

    LL_FLASH_PageErase(ofst / 1024);
    //flashPageErase(ofst / 1024);
}

/// @brief 字节写入
/// @param ofst 地址
/// @param ch   数据
/// @param num  写入字节数
void mcu_bytes_write(HAL_FLASH_ADDR_t ofst, uint8_t* ch, uint16_t num)
{
    // en_flash_lock_t FlashLockNum;       // 定义Flash锁定编号
    // uint32_t LockValue = 0;             // 锁定值
    // uint8_t para = 0;                        // 额外参数
    // Flash_OpModeConfig(FlashWriteMode); // 进入写模式

    // // 根据地址确定锁定区域
    // switch ((ofst / 1024) / 64)
    // {
    // case 0:
    //     FlashLockNum = FlashLock0;
    //     para = ((((ofst / 1024) - 0) / 2));
    //     break;
    // case 1:
    //     FlashLockNum = FlashLock1;
    //     para = ((((ofst / 1024) - 64) / 2));
    //     break;
    // case 2:
    //     FlashLockNum = FlashLock2;
    //     para = ((((ofst / 1024) - 128) / 2));
    //     break;
    // case 3:
    //     FlashLockNum = FlashLock3;
    //     para = ((((ofst / 1024) - 192) / 2));
    //     break;
    // }
    // LockValue |= (1 << para);               // 设置锁定位
    // Flash_LockSet(FlashLockNum, LockValue); // 设置锁定
    
    // if((ofst % 4) == 0 && ((num % 4) == 0))
    // {
    //     ///4字节对齐,按字写入
    //     Flash_Write32(ofst, (uint32_t *)ch, num);           // 写入数据
    // }
    // else if(((ofst % 2) == 0) && ((num % 2) == 0))  
    // {
    //     ///2字节对齐,按半字写入
    //     Flash_Write16(ofst, (uint16_t *)ch, num);           // 写入数据
    // }
    // else
    // {   ///1字节对齐,按字节写入
    //     Flash_Write8(ofst, ch, num);           // 写入数据
    // }
    // Flash_LockAll();                        // 加锁

    LL_FLASH_Program(ofst, (uint32_t)ch, num);
}


 ///以下代码与具体的MCU类型无关，移值无须修改  


///@brief 读取flash数据
///@param ofst 偏移地址
///@param pdat 数据指针
///@param num 读取字节数
bool hal_flash_read(HAL_FLASH_ADDR_t ofst, void* pdat, uint16_t num)
{
    uint8_t* p = (uint8_t*)pdat;
    while(num != 0)
    {
    	*p++ = *(uint8_t*)ofst;
    	ofst++, num--;
    }
	return TRUE;
}

///@brief 写入flash数据,主要用于app
///@param ofst 偏移地址
///@param pdat 数据指针
///@param num 写入字节数
bool hal_flash_write(HAL_FLASH_ADDR_t ofst, const void* pdat, uint16_t num)
{
	uint8_t page_buf[MCU_FLASH_PAGE_PSIZE];
    uint16_t plen;
	uint16_t psize = MCU_FLASH_PAGE_PSIZE;
	const uint8_t* p = (const uint8_t*)pdat;

    if(!IS_DATA_ADDR(ofst, num))
    {
        return false; ///非法地址写入
    }

    while(num > 0)
    {
    	uint16_t i, j;
    	uint16_t idx = ofst % psize;                  // 页内偏移地址
    	ofst &= ~(HAL_FLASH_ADDR_t)(psize - 1);       // 页首偏移地址

        if(idx + num > psize)
        {
            plen = psize - idx;
            num -= plen;
        }
        else
        {
            plen = num;
            num  = 0;
        }

        /* 把FLASH中的页内容读出到页缓冲并改写后，擦除FLASH页并写入数据 */
        HAL_WDG_RESET();
		hal_flash_read(ofst, page_buf, psize); 
		for(i = 0, j = 0; i < plen; i++)
    	{
            uint8_t wb = (pdat == NULL) ? 0 : *p++; // 传空指针用于清零
    		if(page_buf[idx] == wb) j++;
            page_buf[idx++] = wb;
    	}
        if(i != j) ///内容不相同需写入
        {
            HAL_CRITICAL_STATEMENT
            (
                mcu_page_erase(ofst);
                mcu_bytes_write(ofst, page_buf, psize);
            );
        }
        ofst += psize;
    }

    return TRUE;
}

///@brief 页擦除并写入flash数据,主要用于bootload,应用层禁止调用
///@param ofst 偏移地址
///@param pdat 数据指针
///@param num 写入字节数
bool hal_flash_page_program(HAL_FLASH_ADDR_t ofst, const void* pdat, uint16_t num)
{
	uint16_t psize;
	uint8_t* p = (uint8_t*)pdat;

	while(num != 0)
	{
		HAL_WDG_RESET();
		psize = (num >= MCU_FLASH_PAGE_PSIZE) ? MCU_FLASH_PAGE_PSIZE : num;
		HAL_CRITICAL_STATEMENT
		(
			if((ofst % MCU_FLASH_PAGE_PSIZE) == 0) // 如果处在页首址, 则清除该页
			{
				mcu_page_erase(ofst);
			}
			mcu_bytes_write(ofst, p, psize);
		);
        if(memcmp((void*)ofst, p, psize)) return FALSE; ///写后读出来比较
		ofst += psize, p += psize, num -= psize;
	}

	return TRUE;
}


/// @brief 声明hal_flash子模块对象
const struct hal_flash_s hal_flash =
{
	.read    = hal_flash_read,
	.write   = hal_flash_write,
	.program = hal_flash_page_program,
};


/** @} */
/** @} */
/** @} */
