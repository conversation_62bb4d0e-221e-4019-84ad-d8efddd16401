/**
 ******************************************************************************
 * @file    lcd.h
 * <AUTHOR> @date    2024
 * @brief   液晶屏选型及图标映射关系头文件
 * @note
 *
 ******************************************************************************
 *
 * @note
 * Copyright (C) 2024 SheWei Electrics Co.,Ltd. All rights reserved.
 *
 *
 ******************************************************************************/
#ifndef __LCD_H
#define __LCD_H

/* Includes -----------------------------------------------------------------*/
#include "bsp_cfg.h"
#include "bsp_lcd.h"

/// @brief 应用层图标显示映射定义
struct icon_map_s
{
    ICON_TYPE_t app_icon;    /// 应用层图标
    uint8_t     seg_num;     /// 图标的SEG数量
    const void *seg_tab;     /// 图标的SEG组合表指针
};

#if (LCD_TYPE == LCD_MK8702CFSP)    /// 国网三相表液晶
/// LCD_MK8702CFSP 图标定义
#include "lcd_mk8702cfsp.c"
/// @brief 应用层图标显示映射表，只需要定义液晶屏支持的图标
static const struct icon_map_s icon_map_list[] = {
    {.app_icon = TYPE_ICON_TIME, .seg_tab = unit_time, .seg_num = eleof(unit_time)},    // 时间指示符号 hh:mm:ss
    {.app_icon = TYPE_ICON_DATE, .seg_tab = unit_date, .seg_num = eleof(unit_date)},    // 日期指示符号 YY.MM.DD

    {.app_icon = TYPE_ICON_SIGN, .seg_tab = unit_sign, .seg_num = eleof(unit_sign)},    // 正负'-'符号

    {.app_icon = TYPE_ICON_WH, .seg_tab = unit_Wh, .seg_num = eleof(unit_Wh)},             // 有功电能单位符号
    {.app_icon = TYPE_ICON_KWH, .seg_tab = unit_kWh, .seg_num = eleof(unit_kWh)},          // 有功电能单位符号
    {.app_icon = TYPE_ICON_MWH, .seg_tab = unit_mWh, .seg_num = eleof(unit_mWh)},          // 有功电能单位符号
    {.app_icon = TYPE_ICON_VARH, .seg_tab = unit_varh, .seg_num = eleof(unit_varh)},       // 无功电能单位符号
    {.app_icon = TYPE_ICON_KVARH, .seg_tab = unit_kvarh, .seg_num = eleof(unit_kvarh)},    // 无功电能单位符号
    {.app_icon = TYPE_ICON_MVARH, .seg_tab = unit_mvarh, .seg_num = eleof(unit_mvarh)},    // 无功电能单位符号

    {.app_icon = TYPE_ICON_W, .seg_tab = unit_W, .seg_num = eleof(unit_W)},             // 有功功率单位符号,W
    {.app_icon = TYPE_ICON_KW, .seg_tab = unit_kW, .seg_num = eleof(unit_kW)},          // 有功功率单位符号,KW
    {.app_icon = TYPE_ICON_MW, .seg_tab = unit_mW, .seg_num = eleof(unit_mW)},          // 有功功率单位符号,MW
    {.app_icon = TYPE_ICON_VAR, .seg_tab = unit_var, .seg_num = eleof(unit_var)},       // 无功功率单位符号,VAR
    {.app_icon = TYPE_ICON_KVAR, .seg_tab = unit_kvar, .seg_num = eleof(unit_kvar)},    // 无功功率单位符号,KVAR
    {.app_icon = TYPE_ICON_MVAR, .seg_tab = unit_mvar, .seg_num = eleof(unit_mvar)},    // 无功功率单位符号,MVAR
    {.app_icon = TYPE_ICON_VA, .seg_tab = unit_VA, .seg_num = eleof(unit_VA)},          // 视在功率单位符号,VA
    {.app_icon = TYPE_ICON_KVA, .seg_tab = unit_kVA, .seg_num = eleof(unit_kVA)},       // 视在功率单位符号,KVA
    {.app_icon = TYPE_ICON_MVA, .seg_tab = unit_mVA, .seg_num = eleof(unit_mVA)},       // 视在功率单位符号,MVA

    {.app_icon = TYPE_ICON_V1, .seg_tab = unit_V1, .seg_num = eleof(unit_V1)},       // 电压单位符号,V
    {.app_icon = TYPE_ICON_V2, .seg_tab = unit_V2, .seg_num = eleof(unit_V2)},       // 电压单位符号
    {.app_icon = TYPE_ICON_V3, .seg_tab = unit_V3, .seg_num = eleof(unit_V3)},       // 电压单位符号
    {.app_icon = TYPE_ICON_KV1, .seg_tab = unit_kV1, .seg_num = eleof(unit_kV1)},    // 电压单位符号,KV
    {.app_icon = TYPE_ICON_KV2, .seg_tab = unit_kV2, .seg_num = eleof(unit_kV2)},    // 电压单位符号
    {.app_icon = TYPE_ICON_KV3, .seg_tab = unit_kV3, .seg_num = eleof(unit_kV3)},    // 电压单位符号
    {.app_icon = TYPE_ICON_A1, .seg_tab = unit_A1, .seg_num = eleof(unit_A1)},       // 电流单位符号,A
    {.app_icon = TYPE_ICON_A2, .seg_tab = unit_A2, .seg_num = eleof(unit_A2)},       // 电流单位符号
    {.app_icon = TYPE_ICON_A3, .seg_tab = unit_A3, .seg_num = eleof(unit_A3)},       // 电流单位符号
    {.app_icon = TYPE_ICON_KA1, .seg_tab = unit_kA1, .seg_num = eleof(unit_kA1)},    // 电流单位符号,KA
    {.app_icon = TYPE_ICON_KA2, .seg_tab = unit_kA2, .seg_num = eleof(unit_kA2)},    // 电流单位符号
    {.app_icon = TYPE_ICON_KA3, .seg_tab = unit_kA3, .seg_num = eleof(unit_kA3)},    // 电流单位符号
    {.app_icon = TYPE_ICON_Hz, .seg_tab = unit_Hz, .seg_num = eleof(unit_Hz)},       // 频率单位符号,HZ

    {.app_icon = TYPE_ICON_PF, .seg_tab = unit_PF, .seg_num = eleof(unit_PF)},                   // 功率因数单位符号
    {.app_icon = TYPE_ICON_PERCENT, .seg_tab = unit_Percent, .seg_num = eleof(unit_Percent)},    // 功率因数单位符号
    {.app_icon = TYPE_ICON_TEMP, .seg_tab = unit_Temp, .seg_num = eleof(unit_Temp)},             // 功率因数单位符号

    {.app_icon = TYPE_ICON_ACT, .seg_tab = icon_P, .seg_num = eleof(icon_P)},      // 有功符号
    {.app_icon = TYPE_ICON_REACT, .seg_tab = icon_Q, .seg_num = eleof(icon_Q)},    // 无功符号

    {.app_icon = TYPE_ICON_JIAN, .seg_tab = icon_Jian, .seg_num = eleof(icon_Jian)},    // 尖指示符号
    {.app_icon = TYPE_ICON_FENG, .seg_tab = icon_Feng, .seg_num = eleof(icon_Feng)},    // 峰指示符号
    {.app_icon = TYPE_ICON_PING, .seg_tab = icon_Ping, .seg_num = eleof(icon_Ping)},    // 平指示符号
    {.app_icon = TYPE_ICON_GU, .seg_tab = icon_Gu, .seg_num = eleof(icon_Gu)},          // 谷指示符号

    {.app_icon = TYPE_ICON_MAX, .seg_tab = icon_Max, .seg_num = eleof(icon_Max)},    // Max
    {.app_icon = TYPE_ICON_MIN, .seg_tab = icon_Min, .seg_num = eleof(icon_Min)},    // Min
    {.app_icon = TYPE_ICON_AVG, .seg_tab = icon_Avg, .seg_num = eleof(icon_Avg)},    // Avg
    {.app_icon = TYPE_ICON_MD, .seg_tab = icon_MD, .seg_num = eleof(icon_MD)},       // MD
    {.app_icon = TYPE_ICON_THD, .seg_tab = icon_THD, .seg_num = eleof(icon_THD)},    // THD

    {.app_icon = TYPE_ICON_DEMAND, .seg_tab = icon_demand, .seg_num = eleof(icon_demand)},       // 需量
    {.app_icon = TYPE_ICON_LING_XU, .seg_tab = icon_ling_xu, .seg_num = eleof(icon_ling_xu)},    // 零序
    {.app_icon = TYPE_ICON_RI_LI, .seg_tab = icon_ri_li, .seg_num = eleof(icon_ri_li)},          // 日历

    {.app_icon = TYPE_ICON_QIU_HE, .seg_tab = icon_qiuhe, .seg_num = eleof(icon_qiuhe)},    // 求和Σ指示符号

    {.app_icon = TYPE_ICON_AB_A1, .seg_tab = icon_ab_a1, .seg_num = eleof(icon_ab_a1)},    // ab a1指示符号
    {.app_icon = TYPE_ICON_CA_A2, .seg_tab = icon_ca_a2, .seg_num = eleof(icon_ca_a2)},    // ca a2指示符号
    {.app_icon = TYPE_ICON_AB_B1, .seg_tab = icon_ab_b1, .seg_num = eleof(icon_ab_b1)},    // ab b1指示符号
    {.app_icon = TYPE_ICON_BC_B2, .seg_tab = icon_bc_b2, .seg_num = eleof(icon_bc_b2)},    // bc b2指示符号
    {.app_icon = TYPE_ICON_BC_C1, .seg_tab = icon_bc_c1, .seg_num = eleof(icon_bc_c1)},    // bc c1指示符号
    {.app_icon = TYPE_ICON_CA_C2, .seg_tab = icon_ca_c2, .seg_num = eleof(icon_ca_c2)},    // ca c2指示符号
    {.app_icon = TYPE_ICON_N, .seg_tab = icon_n, .seg_num = eleof(icon_n)},                // n指示符号

    {.app_icon = TYPE_ICON_TF1, .seg_tab = icon_tariff_1, .seg_num = eleof(icon_tariff_1)},    // 费率'1'符号 (指示显示内容)
    {.app_icon = TYPE_ICON_TF2, .seg_tab = icon_tariff_2, .seg_num = eleof(icon_tariff_2)},    // 费率'2'符号 (指示显示内容)
    {.app_icon = TYPE_ICON_TF3, .seg_tab = icon_tariff_3, .seg_num = eleof(icon_tariff_3)},    // 费率'3'符号 (指示显示内容)
    {.app_icon = TYPE_ICON_TF4, .seg_tab = icon_tariff_4, .seg_num = eleof(icon_tariff_4)},    // 费率'4'符号 (指示显示内容)
    {.app_icon = TYPE_ICON_TF5, .seg_tab = icon_tariff_5, .seg_num = eleof(icon_tariff_5)},    // 费率'5'符号 (指示显示内容)
    {.app_icon = TYPE_ICON_TF6, .seg_tab = icon_tariff_6, .seg_num = eleof(icon_tariff_6)},    // 费率'6'符号 (指示显示内容)
    {.app_icon = TYPE_ICON_TF7, .seg_tab = icon_tariff_7, .seg_num = eleof(icon_tariff_7)},    // 费率'7'符号 (指示显示内容)
    {.app_icon = TYPE_ICON_TF8, .seg_tab = icon_tariff_8, .seg_num = eleof(icon_tariff_8)},    // 费率'8'符号 (指示显示内容)
    {.app_icon = TYPE_ICON_TF9, .seg_tab = icon_tariff_9, .seg_num = eleof(icon_tariff_9)},    // 费率'9'符号 (指示显示内容)

    {.app_icon = TYPE_ICON_COMM, .seg_tab = icon_mudle_comm, .seg_num = eleof(icon_mudle_comm)},        // 模块通信指示符号
    {.app_icon = TYPE_ICON_ALARM, .seg_tab = icon_alarm, .seg_num = eleof(icon_alarm)},                 // 告警指示符号
    {.app_icon = TYPE_ICON_BAT1, .seg_tab = icon_battery1_low, .seg_num = eleof(icon_battery1_low)},    // 电池1

    {.app_icon = TYPE_ICON_DO_U1, .seg_tab = icon_DO_U1, .seg_num = eleof(icon_DO_U1)},    // DO U1
    {.app_icon = TYPE_ICON_DO_U2, .seg_tab = icon_DO_U2, .seg_num = eleof(icon_DO_U2)},    // DO U2
    {.app_icon = TYPE_ICON_DO_U3, .seg_tab = icon_DO_U3, .seg_num = eleof(icon_DO_U3)},    // DO U3
    {.app_icon = TYPE_ICON_DO_U4, .seg_tab = icon_DO_U4, .seg_num = eleof(icon_DO_U4)},    // DO U4
    {.app_icon = TYPE_ICON_DO_U5, .seg_tab = icon_DO_U5, .seg_num = eleof(icon_DO_U5)},    // DO U5
    {.app_icon = TYPE_ICON_DO_U6, .seg_tab = icon_DO_U6, .seg_num = eleof(icon_DO_U6)},    // DO U6
    {.app_icon = TYPE_ICON_M_U1, .seg_tab = icon_M_U1, .seg_num = eleof(icon_M_U1)},       // M U1
    {.app_icon = TYPE_ICON_M_U2, .seg_tab = icon_M_U2, .seg_num = eleof(icon_M_U2)},       // M U2
    {.app_icon = TYPE_ICON_M_U3, .seg_tab = icon_M_U3, .seg_num = eleof(icon_M_U3)},       // M U3
    {.app_icon = TYPE_ICON_M_U4, .seg_tab = icon_M_U4, .seg_num = eleof(icon_M_U4)},       // M U4
    {.app_icon = TYPE_ICON_M_U5, .seg_tab = icon_M_U5, .seg_num = eleof(icon_M_U5)},       // M U5
    {.app_icon = TYPE_ICON_M_U6, .seg_tab = icon_M_U6, .seg_num = eleof(icon_M_U6)},       // M U6
    {.app_icon = TYPE_ICON_DI_Y1, .seg_tab = icon_DI_Y1, .seg_num = eleof(icon_DI_Y1)},    // DI Y1
    {.app_icon = TYPE_ICON_DI_Y2, .seg_tab = icon_DI_Y2, .seg_num = eleof(icon_DI_Y2)},    // DI Y2
    {.app_icon = TYPE_ICON_DI_Y3, .seg_tab = icon_DI_Y3, .seg_num = eleof(icon_DI_Y3)},    // DI Y3
    {.app_icon = TYPE_ICON_DI_Y4, .seg_tab = icon_DI_Y4, .seg_num = eleof(icon_DI_Y4)},    // DI Y4
    {.app_icon = TYPE_ICON_T_Y1, .seg_tab = icon_T_Y1, .seg_num = eleof(icon_T_Y1)},       // T Y1
    {.app_icon = TYPE_ICON_T_Y2, .seg_tab = icon_T_Y2, .seg_num = eleof(icon_T_Y2)},       // T Y2
    {.app_icon = TYPE_ICON_T_Y3, .seg_tab = icon_T_Y3, .seg_num = eleof(icon_T_Y3)},       // T Y3
    {.app_icon = TYPE_ICON_T_Y4, .seg_tab = icon_T_Y4, .seg_num = eleof(icon_T_Y4)},       // T Y4
};

#elif LCD_TYPE == LCD_SIGNAL
#include ".\lcd_type\lcd_con22.c"

static const struct icon_map_s icon_map_list[] = {
    {.app_icon = TYPE_ICON_TIME, .seg_tab = unit_time, .seg_num = eleof(unit_time)},                   // 时间指示符号 hh:mm:ss
    {.app_icon = TYPE_ICON_DATE, .seg_tab = unit_date, .seg_num = eleof(unit_date)},                   // 日期指示符号 YY.MM.DD
    {.app_icon = TYPE_ICON_SIGN, .seg_tab = unit_sign, .seg_num = eleof(unit_sign)},                   // 正负'-'符号
    {.app_icon = TYPE_ICON_KWH, .seg_tab = unit_kWh, .seg_num = eleof(unit_kWh)},                      // 有功电能单位符号
    {.app_icon = TYPE_ICON_KW, .seg_tab = unit_kW, .seg_num = eleof(unit_kW)},                         // 有功功率单位符号,KW
    {.app_icon = TYPE_ICON_KVA, .seg_tab = unit_kVA, .seg_num = eleof(unit_kVA)},                      // 有功功率单位符号,KW
    {.app_icon = TYPE_ICON_KVAR, .seg_tab = unit_kvar, .seg_num = eleof(unit_kvar)},                   // 有功功率单位符号,KW
    {.app_icon = TYPE_ICON_V, .seg_tab = unit_V, .seg_num = eleof(unit_V)},                            // 电压单位符号
    {.app_icon = TYPE_ICON_A, .seg_tab = unit_A, .seg_num = eleof(unit_A)},                            // 电流单位符号
    {.app_icon = TYPE_ICON_Hz, .seg_tab = unit_Hz, .seg_num = eleof(unit_Hz)},                         // 频率单位符号
    {.app_icon = TYPE_ICON_TOTAL, .seg_tab = icon_total, .seg_num = eleof(icon_total)},                // 总电量指示符号
    {.app_icon = TYPE_ICON_REMAINING, .seg_tab = icon_remaining, .seg_num = eleof(icon_remaining)},    // 剩余电量指示符号
    {.app_icon = TYPE_ICON_RELAY_OFF, .seg_tab = icon_relay_off, .seg_num = eleof(icon_relay_off)},    // 继电器指示符号
    {.app_icon = TYPE_ICON_TARIFF, .seg_tab = icon_tariff, .seg_num = eleof(icon_tariff)},             // 继电器指示符号
    {.app_icon = TYPE_ICON_TF1, .seg_tab = icon_tariff_1, .seg_num = eleof(icon_tariff_1)},            // 费率'1'符号 (指示显示内容)
    {.app_icon = TYPE_ICON_TF2, .seg_tab = icon_tariff_2, .seg_num = eleof(icon_tariff_2)},            // 费率'2'符号 (指示显示内容)
    {.app_icon = TYPE_ICON_TF3, .seg_tab = icon_tariff_3, .seg_num = eleof(icon_tariff_3)},            // 费率'3'符号 (指示显示内容)
    {.app_icon = TYPE_ICON_TF4, .seg_tab = icon_tariff_4, .seg_num = eleof(icon_tariff_4)},            // 费率'4'符号 (指示显示内容)
    {.app_icon = TYPE_ICON_TF5, .seg_tab = icon_tariff_5, .seg_num = eleof(icon_tariff_5)},            // 费率'5'符号 (指示显示内容)
    {.app_icon = TYPE_ICON_TF6, .seg_tab = icon_tariff_6, .seg_num = eleof(icon_tariff_6)},            // 费率'6'符号 (指示显示内容)
    {.app_icon = TYPE_ICON_TF7, .seg_tab = icon_tariff_7, .seg_num = eleof(icon_tariff_7)},            // 费率'7'符号 (指示显示内容)
    {.app_icon = TYPE_ICON_TF8, .seg_tab = icon_tariff_8, .seg_num = eleof(icon_tariff_8)},            // 费率'8'符号 (指示显示内容)
};

#else
#error "LCD类型不匹配!"
#endif

static const uint8_t icon_map_list_num = eleof(icon_map_list);

#endif

/// end of file
