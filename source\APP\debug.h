/******************************************************************************
 *    (c) Copyright 2016, SheWei Electronics Co.,Ltd.
 *    All Rights Reserved
 *
 *    Filename:      debug.h
 *    Describe:
 *    Device:
 *    Compiler:
 *
 *    Created on:
 *    Modify record:
 *
 *******************************************************************************/
#ifndef __DEBUG_H
#define __DEBUG_H

/* Includes -----------------------------------------------------------------*/
#include <stdarg.h>
#include <stdio.h>
#include "bsp_cfg.h"
#if defined(UART_PRINT)
#include "bsp_cfg.h"
#include "hal_uart.h"
#endif
/// 打印开关定义
#define P_ANY true
#define P_NVM false
#define P_UART false
#define P_FLASH false
#define P_TASK false
#define P_ALL false
#define P_EMU false
#define P_PROT false                // 打印保护开关，默认开启
#define P_645 false                 // 645打印开关，默认关闭
#define P_CLOCK false               // 时钟打印开关，默认关闭
#define P_TARIFF false              // 费率打印开关，默认关闭
#define P_STEP false                // 阶梯费率打印开关，默认关闭
#define P_BLE false                 // 蓝牙模块打印开关，默认关闭
#define P_PAY false                 // 扣费模块打印开关，默认关闭
#define P_DEMAND false              // 需量打印开关，默认关闭
#define P_LOAD true                 // 负荷曲线
#define P_DCU true                  // DCU打印开关，默认关闭
#define P_REMOTE_MODULE false        // 远程模块打印开关，默认关闭,
#define P_REMOTE_MODULE_AT false    // 远程模块AT命令打印开关，默认关闭, 这里调试需要用串口打印，否则有异常

/* Export define ------------------------------------------------------------*/
#if defined(DEBUG_PRINT)
#define print_open()
#define dprintf(...) debug_printf(__VA_ARGS__)
#define tmprintf() tprintf()
#elif defined(UART_PRINT)
#define print_open() uart_print_open()
#define dprintf(...) uart_print_send(__VA_ARGS__)
#define tmprintf() tprintf()
#elif defined(SEGGER_PRINT)
#include "SEGGER_RTT.h"
#define BUFFERINDEX 0
#define print_open() SEGGER_RTT_Init();
#define dprintf(...) SEGGER_RTT_printf(BUFFERINDEX, __VA_ARGS__)
#define tmprintf() tprintf()
#else
#define print_open()
#define dprintf(...)
#define tmprintf()
#endif

/// 功能模块调试打印(打印类型,打印信息)
#if defined(DEBUG_PRINT) || defined(UART_PRINT) || defined(SEGGER_PRINT)
#define DBG_MPRINTF(...) mprintf(__VA_ARGS__)
#define DBG_DPRINTF(...) dprintf(__VA_ARGS__)
#define DBG_TPRINTF(...) tmprintf()
#define DBG_PRINTF(n, x, ...) \
    if(n) { DBG_##x##PRINTF(__VA_ARGS__); }
#else
#define DBG_MPRINTF(...)
#define DBG_DPRINTF(...)
#define DBG_TPRINTF(...)
#define DBG_PRINTF(n, x, ...)
#endif

#if defined(DEBUG_PRINT)
extern void debug_printf(const char *fmt, ...);
#endif

#if defined(DEBUG_PRINT) || defined(UART_PRINT) || defined(SEGGER_PRINT)

extern void mprintf(const uint8_t *msg, uint16_t len);
#if defined(UART_PRINT)
extern void uart_print_send(const char *fmt, ...);
extern void uart_print_open(void);
#endif

extern void tprintf(void);

#else
#define printf_enbale(x)
#define printf_enable_check(x) 0
#define mprintf(msg, len)
#define tprintf()
#endif

#endif /* __DEBUG_H */
