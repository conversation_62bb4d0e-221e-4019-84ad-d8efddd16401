/**
 ******************************************************************************
 * @file    hal_gpio.c
 * <AUTHOR> @version V1.0.0
 * @date    2024
 * @brief   本模块完成MCU GPIO的初始化过程.
 * @note    中断中操作GPIO只能用PTCLR，PTSET方式，不能操作其它寄存器！！！！！
 *          如果MCU没有PTCLR\PTSET类似寄存器操作IO口输出，应当使用bit band 方式操作GPIO输出。！！！！！！
 *
 ******************************************************************************
 *
 *
 * Copyright (C) 2024 SheWei Electrics Co.,Ltd. All rights reserved.
 *
 *
 ******************************************************************************/

/* Includes ------------------------------------------------------------------*/
#include "hal_gpio.h"

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
/* 数字IO口功能定义 */
#define GPIO_Mode_AFx 0x08                // GPIO复用功能被使用
#define GPIO_Mode_IN 0x00                 // GPIO输入
#define GPIO_Mode_OUT 0x01                // GPIO输出
#define GPIO_Mode_ANin_0 (0x00 | 0x08)    // 模拟端口
#define GPIO_Mode_AFin_1 (0x10 | 0x08)    // 复用功能1 输入
#define GPIO_Mode_AFin_2 (0x20 | 0x08)    // 复用功能2 输入
#define GPIO_Mode_AFin_3 (0x30 | 0x08)    // 复用功能3 输入
#define GPIO_Mode_AFin_4 (0x40 | 0x08)    // 复用功能4 输入
#define GPIO_Mode_AFin_5 (0x50 | 0x08)    // 复用功能5 输入
#define GPIO_Mode_AFin_6 (0x60 | 0x08)    // 复用功能6 输入
#define GPIO_Mode_AFin_7 (0x70 | 0x08)    // 复用功能7 输入

#define GPIO_Mode_AFo_1 (0x11 | 0x08)    // 复用功能1 输出
#define GPIO_Mode_AFo_2 (0x21 | 0x08)    // 复用功能2 输出
#define GPIO_Mode_AFo_3 (0x31 | 0x08)    // 复用功能3 输出
#define GPIO_Mode_AFo_4 (0x41 | 0x08)    // 复用功能4 输出
#define GPIO_Mode_AFo_5 (0x51 | 0x08)    // 复用功能5 输出
#define GPIO_Mode_AFo_6 (0x61 | 0x08)    // 复用功能6 输出
#define GPIO_Mode_AFo_7 (0x71 | 0x08)    // 复用功能7 输出

#define GPIO_In_Floating 0    // 输入浮空
#define GPIO_In_Up 1          // 输入上拉
#define GPIO_In_Down 2        // 输入下拉

#define PULL_UP 0x80
#define PULL_DOWN 0x40
#define GPIO_Out_PE 0                   // 输出推挽 禁止、下拉
#define GPIO_Out_PP (0 | PULL_UP)       // 输出推挽 上拉
#define GPIO_Out_PD (0 | PULL_DOWN)     // 输出推挽 下拉
#define GPIO_Out_ODE 1                  // 输出开漏 禁止、下拉
#define GPIO_Out_ODP (1 | PULL_UP)      // 输出开漏 上拉
#define GPIO_Out_ODD (1 | PULL_DOWN)    // 输出开漏 下拉

#define GPIO_Output_H 1    // 输出高电平
#define GPIO_Output_L 0    // 输出低电平
#define GPIO_OD_DIS 1      // 关闭开漏
#define GPIO_OD_EN 0       //
/// @brief 这里列出有唤醒(外部)中断功能的GPIO
#define EXTI_RIE 0x0001     // 上升沿中断
#define EXTI_FIE 0x0002     // 下升沿中断
#define EXTI_RFIE 0x0003    // 双边沿中断

void hal_gpio_exti_set(uint8_t irq, void func(void));
void irq_handler_extiA(void);
void irq_handler_extiB(void);
void irq_handler_extiC(void);
void irq_handler_extiD(void);
void irq_handler_extiE(void);
void irq_handler_extiF(void);

/**
 * @brief  配置MCU GPIO功能类型. 利用宏函数定义形式，编译器会自动优化无效内容。另注意效率，复位过的状态则无须配置
 * @param  [in]  x-指定的GPIO
 * @param  [in]  m-GPIO_Mode_IOIN      // 输入
 *                 GPIO_Mode_IOOUT     // 输出
 *                 GPIO_Mode_ANin_0    // 模拟端口
 *                 GPIO_Mode_AFin_1    // 复用功能1 输入
 *                 GPIO_Mode_AFin_2    // 复用功能2 输入
 *                 GPIO_Mode_AFin_3    // 复用功能3 输入
 *                 GPIO_Mode_AFin_4    // 复用功能4 输入
 *                 GPIO_Mode_AFin_5    // 复用功能5 输入
 *                 GPIO_Mode_AFin_6    // 复用功能6 输入
 *                 GPIO_Mode_AFin_7    // 复用功能7 输入
 *                 GPIO_Mode_AFo_1     // 复用功能1 输出
 *                 GPIO_Mode_AFo_2     // 复用功能2 输出
 *                 GPIO_Mode_AFo_3     // 复用功能3 输出
 *                 GPIO_Mode_AFo_4     // 复用功能4 输出
 *                 GPIO_Mode_AFo_5     // 复用功能5 输出
 *                 GPIO_Mode_AFo_6     // 复用功能6 输出
 *                 GPIO_Mode_AFo_7     // 复用功能7 输出
 *
 * @param  [in]  p-GPIO_In_Floating    // 输入浮空 --- 设置输入时有效
 *                 GPIO_Input_Up       // 输入上拉
 *                 GPIO_Input_PD       // 输入下拉
 *
 *                 GPIO_Out_PE         // 输出推挽 禁止、下拉 --- 设置输出时有效
 *                 GPIO_Out_PP         // 输出推挽 上拉
 *                 GPIO_Out_PD         // 输出推挽 下拉
 *                 GPIO_Out_ODE        // 输出开漏 禁止、下拉
 *                 GPIO_Out_ODP        // 输出开漏 上拉
 *                 GPIO_Out_ODD        // 输出开漏 下拉
 *
 * @param  [in]  l-GPIO_Output_H       // 输出高电平 --- 设置输出时有效
 *                 GPIO_Output_L       // 输出低电平
 *
 *                 GPIO_OD_DIS  // 关闭开漏--- 设置输入时有效
 *
 * HC32L19x GPIO寄存器默认值
 * PxDIR = 0xFFFFFFFF; // 所有GPIO口默认配置为输入
 * PxADS = 0x00000000; // 所有GPIO口默认配置为数字端口
 * PxPU = 0x00000000;  // 所有GPIO口默认配置为禁止上拉
 * PxPD = 0x00000000;  // 所有GPIO口默认配置为禁止下拉
 * PxDR = 0x00000000;  // 所有GPIO口默认配置为高驱动
 * PxOD = 0x00000000;  // 所有GPIO口默认配置为禁止开漏
 * PxSEL = 0x00000000; // 所有GPIO口默认配置为GPIO模式
 */
#define _halGpioConfig(port, pin, m, p, l)                                                                        \
    {                                                                                                             \
        if(m == GPIO_Mode_ANin_0) { port->PAADS |= (1UL << (pin)); }                                              \
        else { port->PAADS &= ~(1UL << (pin)); }                                                                  \
        if(m & GPIO_Mode_AFx) { *((uint32_t *)((uint32_t)(&(port->PA00_SEL)) + (pin) * 4)) = ((m & 0xF0) >> 4); } \
        if((m & 0x01) == GPIO_Mode_IN)                                                                            \
        {                                                                                                         \
            port->PADIR |= (1UL << (pin));                                                                        \
            if(p == GPIO_In_Up) { port->PAPU |= (1UL << (pin)), port->PAPD &= ~(1UL << (pin)); }                  \
            else if(p == GPIO_In_Down) { port->PAPU &= ~(1UL << (pin)), port->PAPD |= (1UL << (pin)); }           \
            else { port->PAPU &= ~(1UL << (pin)), port->PAPD &= ~(1UL << (pin)); }                                \
            if(l) { port->PAOD &= ~(1UL << (pin)); }                                                              \
            else { port->PAOD |= (1UL << (pin)); }                                                                \
        }                                                                                                         \
        else                                                                                                      \
        {                                                                                                         \
            port->PADIR &= ~(1UL << (pin));                                                                       \
            if(p & 0x01) { port->PAOD |= (1UL << (pin)); }                                                        \
            else { port->PAOD &= ~(1UL << (pin)); }                                                               \
            if((p & PULL_UP)) { port->PAPU |= (1UL << (pin)), port->PAPD &= ~(1UL << (pin)); }                    \
            else if((p & PULL_DOWN)) { port->PAPU &= ~(1UL << (pin)), port->PAPD |= (1UL << (pin)); }             \
            else { port->PAPU &= ~(1UL << (pin)), port->PAPD &= ~(1UL << (pin)); }                                \
            if((uint32_t)l == GPIO_Output_H) { port->PAOUT |= (1UL << (pin)); }                                   \
            else { port->PAOUT &= ~(1UL << (pin)); }                                                              \
        }                                                                                                         \
    }

#define gpio_config(x, m, p, l) _halGpioConfig(HAL_GPIO_PORT(x), HAL_GPIO_PIN(x), m, p, l)
#define halGpioptupen(x) HAL_GPIO_PTUP_EN(HAL_GPIO_PORT(x), HAL_GPIO_PIN(x))

#define halGetPin(x) (1 << HAL_GPIO_PIN(x))

/// @brief 配置外部中断. 利用宏函数定义形，编译器会自动优化无效内容。
/// @param x-GPIO
/// @param active_level-EXTI_RIE, EXTI_FIE, EXTI_RFIE
#define _gpio_exti_config(port, pin, rfie)                                                               \
    {                                                                                                    \
        port->PAHIE &= ~(1UL << (pin));                                                                  \
        port->PALIE &= ~(1UL << (pin));                                                                  \
        if(rfie & EXTI_RIE) { port->PARIE |= (1UL << (pin)); }                                           \
        if(rfie & EXTI_FIE) { port->PAFIE |= (1UL << (pin)); }                                           \
        if(port == HC_GPIOA) { irq_vector_set(irq_handler_extiA), EnableNvic(PORTA_IRQn, 3, 1); }        \
        else if(port == HC_GPIOB) { irq_vector_set(irq_handler_extiB), EnableNvic(PORTB_IRQn, 3, 1); }   \
        else if(port == HC_GPIOC) { irq_vector_set(irq_handler_extiC), EnableNvic(PORTC_E_IRQn, 3, 1); } \
        else if(port == HC_GPIOD) { irq_vector_set(irq_handler_extiD), EnableNvic(PORTD_F_IRQn, 3, 1); } \
        else if(port == HC_GPIOE) { irq_vector_set(irq_handler_extiE), EnableNvic(PORTC_E_IRQn, 3, 1); } \
        else if(port == HC_GPIOF) { irq_vector_set(irq_handler_extiF), EnableNvic(PORTD_F_IRQn, 3, 1); } \
    }
#define gpio_exti_config(x, rfie) _gpio_exti_config(HAL_GPIO_PORT(x), HAL_GPIO_PIN(x), rfie)

#define _gpio_exti_deConfig(port, pin)                           \
    {                                                            \
        port->PARIE &= ~(1UL << (pin));                          \
        port->PAFIE &= ~(1UL << (pin));                          \
        port->PAHIE &= ~(1UL << (pin));                          \
        port->PALIE &= ~(1UL << (pin));                          \
        if(port == HC_GPIOA) { DisableNvic(PORTA_IRQn); }        \
        else if(port == HC_GPIOB) { DisableNvic(PORTB_IRQn); }   \
        else if(port == HC_GPIOC) { DisableNvic(PORTC_E_IRQn); } \
        else if(port == HC_GPIOD) { DisableNvic(PORTD_F_IRQn); } \
        else if(port == HC_GPIOE) { DisableNvic(PORTC_E_IRQn); } \
        else if(port == HC_GPIOF) { DisableNvic(PORTD_F_IRQn); } \
    }
#define gpio_exti_deconfig(x) _gpio_exti_deConfig(HAL_GPIO_PORT(x), HAL_GPIO_PIN(x))

static funcPointer exti_fun[TYPE_EXTI_NUM];    // typ 1-上升沿，0-下降沿

/// @brief IAR编译环境下的启动文件初始化调用接口,在main函数之前运行，保证在系统初始化之前完成重要GPIO配置
/// @param
/// @return 返回值“0”代表RAM不初始化，返回值“1”代表RAM初始化
#if 0    // defined(__ICCARM__)
int __low_level_init(void)
{
    /// 配置电源检测口
    // gpio_config(PIN_CF2_IN, GPIO_Mode_IN, GPIO_In_Floating, GPIO_Out_OD);
    // gpio_config(PIN_CF1_IN, GPIO_Mode_IN, GPIO_In_Floating, GPIO_Out_OD);
    // gpio_config(PIN_RELAY_LED, GPIO_Mode_OUT, GPIO_Out_PP, GPIO_Output_L);
    // gpio_config(PIN_LVDIN0, GPIO_Mode_AF_1, GPIO_In_Floating, GPIO_Out_OD);

    return 1;
}
#endif

/// @brief  提供给串口驱动调用。有些MCU必须先初始化串口后再打开GPIO，所以不在hal_gpio.c中提前初始化。
void hal_gpio_uart_init(uint8_t com)
{
    if(!M0P_SYSCTRL->PERI_CLKEN0_f.GPIO)    // 如果GPIO外设时钟没有打开，则打开
    {
        M0P_SYSCTRL->PERI_CLKEN0_f.GPIO = 1;    // 打开GPIO外设时钟门控
    }
    switch(com)
    {
        case 0:
#if defined(PIN_UART0_TXD) && defined(PIN_UART0_RXD)
            gpio_config(PIN_UART0_TXD, GPIO_Mode_AFo_1, GPIO_Out_ODE, GPIO_Output_H);
            gpio_config(PIN_UART0_RXD, GPIO_Mode_AFin_1, GPIO_In_Up, GPIO_Output_H);
#endif
            break;
        case 1:
#if defined(PIN_UART1_TXD) && defined(PIN_UART1_RXD)
            gpio_config(PIN_UART1_TXD, GPIO_Mode_AFo_1, GPIO_Out_PE, GPIO_Output_H);
            gpio_config(PIN_UART1_RXD, GPIO_Mode_AFin_1, GPIO_In_Up, GPIO_Output_H);
#endif
            break;
        case 2:
#if defined(PIN_UART2_TXD) && defined(PIN_UART2_RXD)
            gpio_config(PIN_UART2_TXD, GPIO_Mode_AFo_1, GPIO_Out_PE, GPIO_Output_H);
            gpio_config(PIN_UART2_RXD, GPIO_Mode_AFin_1, GPIO_In_Floating, GPIO_Out_ODP);
            // gpio_config(PIN_IR_CTRL, GPIO_Mode_OUT, GPIO_Out_PP, GPIO_Output_L);
#endif
            break;
        case 3:
#if defined(PIN_UART3_RXD) && defined(PIN_UART3_TXD)
            gpio_config(PIN_UART3_RXD, GPIO_Mode_AF_1, GPIO_In_Floating, GPIO_Out_PP);
            gpio_config(PIN_UART3_TXD, GPIO_Mode_AF_1, GPIO_In_Floating, GPIO_Out_PP);
#endif
            break;
        case 4:
#if defined(PIN_UART4_TXD) && defined(PIN_UART4_RXD)
            gpio_config(PIN_UART4_TXD, GPIO_Mode_AFo_6, GPIO_In_Floating, GPIO_Out_PP);
            gpio_config(PIN_UART4_RXD, GPIO_Mode_AFin_6, GPIO_In_Floating, GPIO_Out_PP);
#endif
            break;
        case 5:
            break;
    }
}

/// @brief 所有IO口寄存器监控
/// @param typ :0只扫描电源检测IO口, 1: 扫描所有IO口
void hal_gpio_monitor(uint8_t typ)
{
    if(!M0P_SYSCTRL->PERI_CLKEN0_f.GPIO) { M0P_SYSCTRL->PERI_CLKEN0_f.GPIO = 1; }    // 打开GPIO外设时钟门控
    if(0xE000 != (M0P_GPIO->PCADS & 0xE000)) { M0P_GPIO->PCADS |= 0xE000; }          // LVD-PC13, XTL-PC14 PC15
}

void hal_gpio_reset(void)
{
    // 配置为数字端口
    M0P_GPIO->PAADS = 0;
    M0P_GPIO->PBADS = 0;
    M0P_GPIO->PCADS = 0xE000;
    M0P_GPIO->PDADS = 0;
    M0P_GPIO->PEADS = 0;
    M0P_GPIO->PFADS = 0;

    // 配置为端口输入
    M0P_GPIO->PADIR = 0xFFFF;
    M0P_GPIO->PBDIR = 0xFFFF;
    M0P_GPIO->PCDIR = 0xFFFF;
    M0P_GPIO->PDDIR = 0xFFFF;
    M0P_GPIO->PEDIR = 0xFFFF;
    M0P_GPIO->PFDIR = 0xFFFF;

    // 输入下拉
    M0P_GPIO->PAPD = 0xFFFF;
    M0P_GPIO->PBPD = 0xFFFF;
    M0P_GPIO->PCPD = 0x1FFF;
    M0P_GPIO->PDPD = 0xFFFF;
    M0P_GPIO->PEPD = 0xFFFF;
    M0P_GPIO->PFPD = 0xFFFF;
}

/// @brief 配置GPIO外部中断, 开启NVIC中断
/// @param mode 0开启中断，1关闭中断
void hal_gpio_exti_config(uint8_t mode)
{
    if(!mode)
    {
#ifdef PIN_METER_COVER
        gpio_exti_config(PIN_METER_COVER, EXTI_RFIE);    // 开盖检测
#endif
#ifdef PIN_TEM_COVER
        gpio_exti_config(PIN_TEM_COVER, EXTI_RFIE);
#endif
#ifdef PIN_KEY_DWN
        gpio_exti_config(PIN_KEY_DWN, EXTI_RIE);
#endif
#ifdef PIN_KEY_UP
        gpio_exti_config(PIN_KEY_UP, EXTI_RIE);
#endif
    }
    else
    {
#ifdef PIN_METER_COVER
        gpio_exti_deconfig(PIN_METER_COVER);
#endif
#ifdef PIN_TEM_COVER
        gpio_exti_deconfig(PIN_TEM_COVER);
#endif
#ifdef PIN_KEY_DWN
        gpio_exti_deconfig(PIN_KEY_DWN);
#endif
#ifdef PIN_KEY_UP
        gpio_exti_deconfig(PIN_KEY_UP);
#endif
    }
}

/// @brief 正常上电初始化GPIO
/// 1，输出IO的高低电平依据电路控制逻辑相应选择GPIO_Output_H或者GPIO_Output_L。
/// 2，外设功能IO配置一律采用GPIO_In_Floating，GPIO_Out_PP。
/// 3，不建议上电情况下使用GPIO中断功能,脉冲除外。
/// 4，这里只需配置用到的IO口即可，其他IO口默认配置为GPIO模式，并且关闭输入和输出使能，避免引脚浮空漏电。
/// 5，如果需要使用按键GPIO中断功能，则在初始化完成后，调用hal_gpio_exti_config(1)开启中断。
/// 6，如果需要使用按键GPIO中断功能，则在初始化完成后，调用hal_gpio_exti_set(irq, func)设置中断回调函数。
/// 7，如果需要使用按键GPIO中断功能，则在中断回调函数中调用hal_gpio_exti_clear(irq)清除中断标志。
/// 8，应用中IICSDA设置为
/// @param

void hal_gpio_init(void)
{
    M0P_SYSCTRL->PERI_CLKEN0_f.GPIO = 1;    // 打开GPIO外设时钟门控

    hal_gpio_reset();
    /* Analog Input ------------------------------------------------------------*/

    // #ifdef PIN_BATT_EXT
    //     gpio_config(PIN_BATT_EXT, GPIO_Mode_AF_2, GPIO_In_Floating, GPIO_Out_OD);
    // #endif
    // #ifdef PIN_BATT_IN
    //     gpio_config(PIN_BATT_IN, GPIO_Mode_AF_2, GPIO_In_Floating, GPIO_Out_OD);
    // #endif

    //     hal_gpio_exti_config(1);    // 关闭按键外部中断
    //     /* Digital Input -----------------------------------------------------------*/
    // #ifdef PIN_KEY_DWN
    //     gpio_config(PIN_KEY_DWN, GPIO_Mode_IN, GPIO_In_Floating, GPIO_Out_OD);
    // #endif
    // #ifdef PIN_KEY_UP
    //     gpio_config(PIN_KEY_UP, GPIO_Mode_IN, GPIO_In_Floating, GPIO_Out_OD);
    // #endif
    // #ifdef PIN_METER_COVER
    //     gpio_config(PIN_METER_COVER, GPIO_Mode_IN, GPIO_In_Floating, GPIO_Out_OD);
    // #endif
    // #ifdef PIN_TEM_COVER
    //     gpio_config(PIN_TEM_COVER, GPIO_Mode_IN, GPIO_In_Floating, GPIO_Out_OD);
    // #endif
    gpio_config(PIN_MCU_DO0, GPIO_Mode_OUT, GPIO_Out_PP, GPIO_Output_H);

    //     gpio_config(PIN_NET_STATUS, GPIO_Mode_OUT, GPIO_Out_PP, GPIO_Output_L);     // 网络状态灯
    //     gpio_config(PIN_VFLASH_CTRL, GPIO_Mode_OUT, GPIO_Out_PP, GPIO_Output_L);    // flash控制

    //     gpio_config(PIN_RELAY_OFF, GPIO_Mode_OUT, GPIO_Out_PP, GPIO_Output_L);
    //     gpio_config(PIN_RELAY_ON, GPIO_Mode_OUT, GPIO_Out_PP, GPIO_Output_L);

    gpio_config(PIN_LCD_BG, GPIO_Mode_OUT, GPIO_Out_PP, GPIO_Output_H);
    //     gpio_config(PIN_RELAY_LED, GPIO_Mode_OUT, GPIO_Out_PP, GPIO_Output_L);

    //     gpio_config(PIN_POWER_CTRL, GPIO_Mode_OUT, GPIO_Out_PP, GPIO_Output_L);
    //     // gpio_config(PIN_VLCD_CTRL,   GPIO_Mode_OUT, GPIO_Out_OD, GPIO_Output_H);
    //     gpio_config(PIN_VLCD_CTRL, GPIO_Mode_OUT, GPIO_Out_PP, GPIO_Output_L);

    //     gpio_config(PIN_EMU_CTL, GPIO_Mode_OUT, GPIO_Out_PP, GPIO_Output_H);

    //     gpio_config(PIN_VFLASH_CTRL, GPIO_Mode_OUT, GPIO_Out_PP, GPIO_Output_L);    // flash 电源控制
}

/**
 * @brief  低功耗运行环境GPIO初始化.
 *         默认所有功能引脚被配置为GPIO模式，开漏输出高。
 */
void hal_gpio_init_nopower(void)
{
    // 打开GPIO外设时钟门控
    Sysctrl_SetPeripheralGate(SysctrlPeripheralGpio, TRUE);

    hal_gpio_reset();
    // swd as gpio
    // Sysctrl_SetFunc(SysctrlSWDUseIOEn, TRUE);

    // #ifdef PIN_EMU_CTL
    //     gpio_config(PIN_EMU_CTL, GPIO_Mode_OUT, GPIO_Out_PP, GPIO_Output_L);
    // #endif

    // #ifdef PIN_KEY_DWN
    //     gpio_config(PIN_KEY_DWN, GPIO_Mode_IN, GPIO_In_Floating, GPIO_Out_OD);    // 先设置为输入，再设置为中断
    //     gpio_config(PIN_KEY_DWN, GPIO_Mode_AF_1, GPIO_In_Floating, GPIO_Out_OD);
    // #endif
    // #ifdef PIN_KEY_UP
    //     gpio_config(PIN_KEY_UP, GPIO_Mode_IN, GPIO_In_Floating, GPIO_Out_OD);
    //     gpio_config(PIN_KEY_UP, GPIO_Mode_AF_1, GPIO_In_Floating, GPIO_Out_OD);
    // #endif
    // #ifdef PIN_METER_COVER
    //     gpio_config(PIN_METER_COVER, GPIO_Mode_IN, GPIO_In_Floating, GPIO_Out_OD);
    //     gpio_config(PIN_METER_COVER, GPIO_Mode_AF_1, GPIO_In_Floating, GPIO_Out_OD);
    // #endif
    // #ifdef PIN_TEM_COVER
    //     gpio_config(PIN_TEM_COVER, GPIO_Mode_IN, GPIO_In_Floating, GPIO_Out_OD);
    //     gpio_config(PIN_TEM_COVER, GPIO_Mode_AF_1, GPIO_In_Floating, GPIO_Out_OD);
    // #endif

    //     /// 开启引脚中断
    //     hal_gpio_exti_config(0);
    //     gpio_config(PIN_EE_SCL, GPIO_Mode_IN, GPIO_In_Floating, GPIO_Out_OD);
    //     gpio_config(PIN_EE_SCL, GPIO_Mode_IN, GPIO_In_Floating, GPIO_Out_OD);

    /// 未使用的引进和断电后相当于浮空的引进设置为开漏输出高
}

/// @brief FLASH片选、时钟、命令、数据引脚初始化
/// @param
void hal_gpio_flash_init(GPIO_INIT_TYPE_t type)
{
    // gpio_config(FLASH_PW_PIN, GPIO_Mode_OUT, GPIO_Out_PP, GPIO_Output_H);
    if(type == GPIO_OPEN)
    {
        // gpio_out_L(FLASH_PW_PIN);    // 电源控制
        gpio_config(PIN_FLASH_CS, GPIO_Mode_AFo_1, GPIO_Out_PP, GPIO_Output_H);
        gpio_config(PIN_FLASH_SCLK, GPIO_Mode_AFo_1, GPIO_Out_PP, GPIO_Output_L);    // SCLK常态低电平 CPOL-0，CPHA-0(第一个沿采数据)
        gpio_config(PIN_FLASH_MOSI, GPIO_Mode_AFo_1, GPIO_Out_PE, GPIO_Output_H);
        gpio_config(PIN_FLASH_MISO, GPIO_Mode_AFin_1, GPIO_In_Up, GPIO_Out_ODE);    /// 没有外部上拉
    }
    else if(type == GPIO_CLOSE)
    {
        // gpio_config(FLASH_PW_PIN, GPIO_Mode_OUT, GPIO_Out_PP, GPIO_Output_H);
        gpio_config(PIN_FLASH_CS, GPIO_Mode_OUT, GPIO_Out_ODE, GPIO_Output_H);
        gpio_config(PIN_FLASH_SCLK, GPIO_Mode_OUT, GPIO_Out_ODE, GPIO_Output_H);
        gpio_config(PIN_FLASH_MOSI, GPIO_Mode_OUT, GPIO_Out_ODE, GPIO_Output_H);
        gpio_config(PIN_FLASH_MISO, GPIO_Mode_OUT, GPIO_Out_ODE, GPIO_Output_H);
    }
}

/// @brief EEPROM时钟、数据引脚初始化
/// @param
void hal_gpio_eeprom_init(GPIO_INIT_TYPE_t type)
{
    if(type == GPIO_OPEN)
    {
        gpio_config(PIN_EE_SDA, GPIO_Mode_IN, GPIO_In_Floating, GPIO_OD_EN);
        gpio_out_L(PIN_EE_SDA);
        gpio_config(PIN_EE_SCL, GPIO_Mode_OUT, GPIO_Out_ODE, GPIO_Output_H);
    }
}

/// @brief LCD电源，时钟、数据引脚初始化
/// @param type
void hal_gpio_lcd_init(GPIO_INIT_TYPE_t type)
{
    if(type == GPIO_OPEN)
    {
        gpio_config(PIN_VLCD_CTRL, GPIO_Mode_OUT, GPIO_Out_ODE, GPIO_Output_L);

        gpio_config(PIN_LCD_SDA, GPIO_Mode_OUT, GPIO_Out_ODE, GPIO_Output_H);
        gpio_config(PIN_LCD_SCL, GPIO_Mode_OUT, GPIO_Out_ODE, GPIO_Output_H);
    }
    else if(type == GPIO_CLOSE)
    {
        gpio_config(PIN_VLCD_CTRL, GPIO_Mode_OUT, GPIO_Out_ODE, GPIO_Output_H);
        gpio_config(PIN_LCD_SDA, GPIO_Mode_OUT, GPIO_Out_ODE, GPIO_Output_H);
        gpio_config(PIN_LCD_SCL, GPIO_Mode_OUT, GPIO_Out_ODE, GPIO_Output_H);
    }
}

void hal_gpio_mic_init(GPIO_INIT_TYPE_t type)
{
    if(type == GPIO_OPEN)
    {
        // #define PIN_HT_DOUT HC_GPIOB, 12     // -o  计量芯片MCU数据输出
        // #define PIN_HT_DIN HC_GPIOB, 13      // -i  计量芯片MCU数据输入
        // #define PIN_HT_CLK HC_GPIOB, 14      // -o  计量芯片时钟输出
        // #define PIN_HT_CS HC_GPIOB, 15       // -o  计量芯片片选
        // gpio_out_L(PIN_EMU_CTL);
        gpio_config(PIN_HT_CS, GPIO_Mode_AFo_1, GPIO_Out_PP, GPIO_Output_H);
        gpio_config(PIN_HT_CLK, GPIO_Mode_AFo_1, GPIO_Out_PP, GPIO_Output_L);    // SCLK常态低电平 CPOL-0，CPHA-1(第二个沿采数据)
        gpio_config(PIN_HT_DOUT, GPIO_Mode_AFo_1, GPIO_Out_PP, GPIO_Output_H);
        gpio_config(PIN_HT_DIN, GPIO_Mode_AFin_1, GPIO_In_Up, GPIO_Out_ODE);
    }
    else
    {
        // gpio_out_H(PIN_EMU_CTL);
        gpio_config(PIN_HT_CS, GPIO_Mode_IN, GPIO_In_Down, GPIO_OD_DIS);
        gpio_config(PIN_HT_CLK, GPIO_Mode_IN, GPIO_In_Down, GPIO_OD_DIS);
        gpio_config(PIN_HT_DOUT, GPIO_Mode_IN, GPIO_In_Down, GPIO_OD_DIS);
        gpio_config(PIN_HT_DIN, GPIO_Mode_IN, GPIO_In_Down, GPIO_OD_DIS);
    }
}

/// @brief 设置秒脉冲输出
/// @param mode mode=1:输出秒脉冲，0- 关闭
void hal_pulse_out_mode(uint8_t mode)
{
    // if(mode) { gpio_config(PIN_SECOND_TOUT, GPIO_Mode_AF_1, GPIO_In_Floating, GPIO_Out_PP); }
    // else { gpio_config(PIN_SECOND_TOUT, GPIO_Mode_OUT, GPIO_Out_OD, GPIO_Output_H); }
}

static void hal_gpio_remote_module_init(GPIO_INIT_TYPE_t type)
{
    if(type == GPIO_OPEN)
    {
        gpio_config(PIN_4G_POWER, GPIO_Mode_OUT, GPIO_Out_PE, GPIO_Output_L);
        gpio_config(PIN_4G_PWRKEY, GPIO_Mode_OUT, GPIO_Out_PE, GPIO_Output_L);
        gpio_config(PIN_4G_RST, GPIO_Mode_OUT, GPIO_Out_PE, GPIO_Output_L);
        gpio_config(PIN_4G_WAKEUP, GPIO_Mode_OUT, GPIO_Out_PE, GPIO_Output_H);
    }
    else
    {
        gpio_config(PIN_4G_PWRKEY, GPIO_Mode_OUT, GPIO_Out_PE, GPIO_Output_H);
        gpio_config(PIN_4G_RST, GPIO_Mode_OUT, GPIO_Out_PE, GPIO_Output_H);
        gpio_config(PIN_4G_WAKEUP, GPIO_Mode_OUT, GPIO_Out_PE, GPIO_Output_H);
    }
}

void hal_gpio_exti_set(uint8_t irq, void func(void))
{
    exti_fun[irq] = func;
}

void irq_handler_extiA(void)
{
    volatile uint32_t tmp = M0P_GPIO->PA_STAT;

    // if(exti_fun[0] != NULL) { exti_fun[0](); }

    M0P_GPIO->PA_ICLR = ~tmp;
}
void irq_handler_extiB(void)
{
    volatile uint32_t tmp = M0P_GPIO->PB_STAT;

#if IRQ_PORT_B04
    if((tmp & (1 << 4)) && exti_fun[TYPE_PB04] != NULL) { exti_fun[TYPE_PB04](); }
#endif
#if IRQ_PORT_B05
    if((tmp & (1 << 5)) && exti_fun[TYPE_PB05] != NULL) { exti_fun[TYPE_PB05](); }
#endif

    M0P_GPIO->PB_ICLR = ~tmp;
}
void irq_handler_extiC(void)
{
    volatile uint32_t tmp = M0P_GPIO->PC_STAT;

    // if(exti_fun[0] != NULL) { exti_fun[0](); }

    M0P_GPIO->PA_ICLR = ~tmp;
}
void irq_handler_extiD(void)
{
    volatile uint32_t tmp = M0P_GPIO->PA_STAT;

    // if(exti_fun[0] != NULL) { exti_fun[0](); }

    M0P_GPIO->PA_ICLR = ~tmp;
}
void irq_handler_extiE(void)
{
    volatile uint32_t tmp = M0P_GPIO->PA_STAT;

    // if(exti_fun[0] != NULL) { exti_fun[0](); }

    M0P_GPIO->PA_ICLR = ~tmp;
}
void irq_handler_extiF(void)
{
    volatile uint32_t tmp = M0P_GPIO->PA_STAT;

#if IRQ_PORT_F00
    if((tmp & (1 << 0)) && exti_fun[TYPE_PF00] != NULL) { exti_fun[TYPE_PF00](); }
#endif
#if IRQ_PORT_F01
    if((tmp & (1 << 1)) && exti_fun[TYPE_PF01] != NULL) { exti_fun[TYPE_PF01](); }
#endif

    M0P_GPIO->PA_ICLR = ~tmp;
}

/// @brief 声明hal_gpio子模块对象
const struct hal_gpio_t hal_gpio = {
    .init           = hal_gpio_init,
    .init_nopower   = hal_gpio_init_nopower,
    .uart_init      = hal_gpio_uart_init,
    .pulse_out_mode = hal_pulse_out_mode,
    .monitor        = hal_gpio_monitor,
    .exti_set       = hal_gpio_exti_set,
    .data_flash     = hal_gpio_flash_init,
    .ext_eeprom     = hal_gpio_eeprom_init,
    .ext_lcd        = hal_gpio_lcd_init,
    .mic_init       = hal_gpio_mic_init,
    .remote_module  = hal_gpio_remote_module_init,
};

/** @} */
/** @} */
/** @} */
