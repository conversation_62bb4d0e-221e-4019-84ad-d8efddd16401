/**
 ******************************************************************************
 * @file    boot_cfg.c
 * <AUTHOR> @version V1.0
 * @date    2025-03-04
 * @brief   本文件主要用于进入BOOT模式下的配置初始化以及相关调用接口
 *
 ******************************************************************************
 *
 * @note
 * Copyright (C) SheWei Electrics Co.,Ltd. All rights reserved.
 *
 *
 ******************************************************************************/
#include <string.h>
#include "crc.h"
#include "bsp_cfg.h"
#include "ext_flash.h"

// 需外部提供的MCU子模块接口
extern McuCoreStus_s hal_mcu_core_init(void);
extern void          hal_mcu_sleep(void);
extern bool          hal_mcu_pwron_query(void);

// 需外部提供的GPIO子模块接口 
extern void hal_gpio_init(void);
extern void hal_gpio_init_nopower(void);

// 需外部提供的RTC子模块接口
extern HAL_RTC_STUS hal_rtc_init(uint8 mode);
extern bool         hal_rtc_time_get(struct rtc_t* rtc);
extern bool         hal_rtc_irq_set(HAL_RTC_ALARM_MODE mode, void func(void));

// 需外部提供的UART子模块接口
extern void hal_uart_open(HAL_UART_TYPE com, 
                          HAL_UART_CTRL ctrl, 
                          HAL_UART_CHAR_TYPE format, 
                          HAL_UART_BAUDE_TYPE baude,
                          uint8_t *rxbuf, uint16_t size);
extern void hal_uart_print(HAL_UART_TYPE com, char ch);
extern bool hal_uart_scan(HAL_UART_TYPE com, uint8_t *ch, uint16_t msWait);

// 需外部提供的FLASH子模块接口
extern bool bsp_flash_read_bytes(uint32_t addr, void *dat, uint32_t len);
extern bool bsp_extflash_program_bytes(uint32_t addr, const void *dat, uint32_t len);
extern bool hal_flash_page_program(HAL_FLASH_ADDR_t ofst, const void *pdat, uint16_t num);

/* Private define ------------------------------------------------------------*/
static HAL_UART_TYPE using_uart;          /// ISP正在使用的串口类型
static uint8_t       uart_timeout_cnt;    /// 串口超时计数器

/* Private functions ---------------------------------------------------------*/

/// @brief 中断向量配置
/// @param irq 
/// @param vec 
void boot_intvec_set(int irq, void (*vec)(void))
{
    int_vector_set(irq, vec);
}

/// @brief 进入BOOT时初始化配置MCU
/// @param  
void boot_init(void)
{
    HAL_DISABLE_INTERRUPTS();    // 关闭所有中断

    /// @note MCU核初始化
    if(hal_mcu_core_init().power_on_rst)
    {
        /// @note GPIO正常初始化
        hal_gpio_init();
    }
    else
    {
        /// @note GPIO低功耗初始化
        hal_gpio_init_nopower();

        /// @note RTC初始化, 并使能秒中断
        hal_rtc_init(0);
        hal_rtc_irq_set(SEC_ALARM, NULL);    // RTC秒中断使能
        while(1) { hal_mcu_sleep(); }
    }

#if IAP_SUPPORT
    boot.file_open();
#endif
#if IAP_DISPLAY_EN
    boot.lcd_open();
#endif
}

/// @brief codeflash编程接口
/// @param addr 起始地址必须是页对齐的！！！！
/// @param dat  数据接口
/// @param len          
/// @return 
bool boot_program(uint32_t addr, const void *dat, uint16_t len)
{
    return hal_flash_page_program(addr, dat, len);
}

/// @brief 复位MCU
/// @param  
void boot_reset(void)
{
    HAL_SYSTEM_RESET();
}

/// @brief ISP串口初始化
/// @param  
void boot_serial_open(void)
{
    using_uart       = COM_RS4851;    /// 如果是远红外，建议仅使用RS485进行ISP
    uart_timeout_cnt = 0;

#ifdef COM_RS4851
    hal_uart_open(COM_RS4851, UC_RS485_DE, CHAR_8N1, BAUDE_19200BPS, NULL, 0);
#endif
}

void boot_time_get(uint8_t time[6])
{
    struct rtc_t rtc;
    hal_rtc_time_get(&rtc);
    memcpy(time, &rtc, 6);
}

/// @brief 串口发送1个字符
/// @param ch  发送的字符
void boot_serial_send(unsigned char ch)
{
    // 这里监控MCU供电, 如果电源异常时复位退出在线升级
    if(hal_mcu_pwron_query()) HAL_SYSTEM_RESET();

    // 发送1个字符
    hal_uart_print(using_uart, ch);
}

/// @brief 串口接收1个字符
/// @param ch        接受字符指针
/// @param time_out  接受超时时间(单位:ms)
/// @return          接收成功返回true, 超时返回false
char boot_serial_recv(unsigned char *ch, unsigned int time_out)
{
    // 扫描接收1个字符
    switch(using_uart)
    {
//        case COM_IR:
//            if(!hal_uart_scan(COM_IR, ch, time_out))
//            {
//                if(++uart_timeout_cnt >= 3)
//                {
//#ifdef COM_RS4851
//                    using_uart = COM_RS4851;    /// COM_IR超时
//#endif
//                    uart_timeout_cnt = 0;
//                }
//                return false;
//            }
//            else
//            {
//                uart_timeout_cnt = 0;
//                return true;
//            }

#ifdef COM_RS4851
        case COM_RS4851:
            if(!hal_uart_scan(COM_RS4851, ch, time_out))
            {
                if(++uart_timeout_cnt >= 3)
                {
                    using_uart       = COM_RS4851;    /// 
                    uart_timeout_cnt = 0;
                }
                return false;
            }
            else
            {
                uart_timeout_cnt = 0;
                return true;
            }
#endif
    }

    return false;
}

#if IAP_SUPPORT
/// @brief 程序文件初始化调用接口
/// @param  
void boot_file_open(void)
{
#if IAP_EXT_NVM && USE_DATAFLASH
    hal_spi_open(COM_DATAFLASH, 2000);    // 2Mbps
#endif
}

/// @brief 程序文件读取调用接口
/// @param addr 地址
/// @param dat  程序文件存放地址
/// @param len  读取长度
/// @return     true: 成功, false: 失败
bool boot_file_read(uint32_t addr, void *dat, uint16_t len)
{
    /// @note 这里需要监控MCU供电, 如果电源异常时复位退出在线升级
    if(hal_mcu_pwron_query()) HAL_SYSTEM_RESET();

#if IAP_EXT_NVM && USE_DATAFLASH
    volatile uint16_t cs1, cs2;
    bsp_flash_read_bytes(addr, dat, len);       /// 第一次读
    cs1 = crc16_update_revtab(0, dat, len);     /// 计算读出内容的CRC
    bsp_flash_read_bytes(addr, dat, len);       /// 第二次读
    cs2 = crc16_update_revtab(0, dat, len);
    return boolof(cs1 == cs2);                  /// 比较两次读后的CRC
#else
    memcpy(dat, (void *)addr, len);
    return true;
#endif
}

/// @brief 程序文件存储调用接口，支持写入外部flash
/// @param addr 注意写入片上时，地址必须是页对齐的！！！！
/// @param dat  数据接口
/// @param len  写入长度
/// @return     true: 成功, false: 失败
bool boot_file_write(uint32_t addr, void *dat, uint16_t len)
{
    /// @note 这里需要监控MCU供电, 如果电源异常时复位退出在线升级
    if(hal_mcu_pwron_query()) HAL_SYSTEM_RESET();

#if IAP_EXT_NVM && USE_DATAFLASH
    volatile uint16_t cs1, cs2;
    cs1 = crc16_update_revtab(0, dat, len);    /// 计算写前内容的CRC
    if(!bsp_extflash_program_bytes(addr, dat, len)) return false;
    bsp_flash_read_bytes(addr, dat, len);      /// 写入后读回
    cs2 = crc16_update_revtab(0, dat, len);
    return boolof(cs1 == cs2);                 /// 比较写前和读后的CRC
#else
    return boot_program(addr, dat, len);
#endif
}
#endif

#if IAP_DISPLAY_EN
/// @brief 打开LCD
/// @param
void boot_lcd_open(void)
{
    hal_lcd.open();
}

/// @brief 升级信息显示
/// @param course 0: ISP-1, 1: ISP-2, 2: IAP-1, 3: IAP-2, 4: IAP-o, 5: IAP-e
/// @param 动态条显示参数
void boot_lcd_printf(uint8_t course, ...)
{
    const uint8_t   isp_[4]    = {CHAR_I, CHAR_S, CHAR_P, CHAR_};
    const uint8_t   cstr[6]    = {CHAR_1, CHAR_2, CHAR_1, CHAR_2, CHAR_o, CHAR_E};
    const uint8_t   pbar[3][3] = {CHAR__, SPACE, SPACE, CHAR__, CHAR__, SPACE, CHAR__, CHAR__, CHAR__};
    uint8_t         i, pos, ch;
    uint8_t         buf[8];
    const SEG_TYPE *ptr;
    int             v;
    va_list         ParamList;

    memcpy(buf, (uint8_t *)isp_, 4);
    if(course > 1) buf[1] = CHAR_A;
    buf[4] = cstr[course];

    /// @note 动态条显示
    if((course == 1) || (course == 3))
    {
        va_start(ParamList, course);
        v = va_arg(ParamList, int);
        va_end(ParamList);
        memcpy(buf + 5, (uint8_t *)pbar[(v / 10) % 3], 3);
    }
    else { memset(buf + 5, SPACE, 3); }

    hal_lcd.all_seg_clr();
    ptr = digit_screen[0].seg_tab;
    for(pos = 0; pos < 8; pos++)
    {
        ch = buf[pos];
        if(ch != SPACE)
        {
            for(i = 0; i < 7; i++)
            {
                hal_lcd.light(*ptr++, ch);
                ch = ch >> 1;
            }
        }
    }
    hal_lcd.refresh();
}
#endif

/// @brief 声明BOOT底层驱动模块
const struct boot_t boot = {
    .init        = boot_init,
    .reset       = boot_reset,
    .program     = boot_program,
    .serial_open = boot_serial_open,
    .serial_recv = boot_serial_recv,
    .serial_send = boot_serial_send,
#if IAP_SUPPORT
    .file_open  = boot_file_open,
    .file_read  = boot_file_read,
    .file_write = boot_file_write,
#endif
    .time_get   = boot_time_get,
#if IAP_DISPLAY_EN
    .lcd_open   = boot_lcd_open,
    .lcd_printf = boot_lcd_printf,
#endif
};

// End of boot_cfg.c
