/*
 * @description: 
 * @date: Do not edit
 * @lastAuthor: 
 * @lastEditTime: Do not edit
 * @filePath: Do not edit
 */
/******************************************************************************
*    (c) Copyright 2016, SheWei Electronics Co.,Ltd.
*    All Rights Reserved
*
*    Filename:      DLT645_2007.h
*    Describe:      
*
*    Device:
*    Compiler:
*
*    Created on:
*    Modify record:
*
*******************************************************************************/

#ifndef DLT645_2007_H
#define DLT645_2007_H
#include "typedef.h"

typedef enum
{
    ACK_NULL,    /// 协议未知，无需回复
    ACK_NO,      /// 协议确认，但无需回复
    ACK_YES,     /// 协议确认，需要回复
}ACK_TYPE_t;


struct dlt645_2007_s
{
    /// @brief 初始化
    void (*init)(uint8_t chn, uint8_t *buff);
    /// @brief 协议处理
    uint16_t (*msg_process)(uint8_t chn, uint8_t *ack, uint16_t len);
    /// @brief 协议发送
    uint32_t (*get_opt_code)(void);
};

extern const struct dlt645_2007_s DLT645; 

#endif /* DLT645_2007_H */

