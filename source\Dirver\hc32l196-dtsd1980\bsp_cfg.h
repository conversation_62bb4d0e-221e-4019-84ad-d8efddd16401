 /**
  ******************************************************************************
  * @file    bsp_cfg.h
  * <AUTHOR> @version V1.0
  * @date    2024-08-04
  * @brief   BSP层配置文件
  *
  ******************************************************************************
  *
  * @note
  * Copyright (C) SheWei Electrics Co.,Ltd. All rights reserved.
  *
  *
  ******************************************************************************/
#ifndef __BSP_CFG_H
#define __BSP_CFG_H

/* Includes ------------------------------------------------------------------*/
#include "hal_inc.h"

/* Exported types ------------------------------------------------------------*/
/* Exported defines ----------------------------------------------------------*/
/* 定义硬件版本号 */
#define HDW_VERSION             "DTSD1980"
#define HDW_VERSION_S           "V1.01_2024.05.21"

/* 硬件配置: 计量芯片相关 */
#define EMU_VIRTUAL                              0             ///@虚拟计量芯片
#define EMU_HT7136                               1             ///@HT7136计量芯片
#define EMU_TYPE                                 EMU_HT7136   ///@配置计量芯片类型定义

#define USE_HDW_RST_MIC                          0             ///@配置是否支持硬件复位计量芯片
#define USE_EMU_AT_LOSS_VOLTAGE                  1             ///@配置是否支持全失压检测

/* SAG类型选择-用来提前判掉电 */
#define SAG_NULL                                 0
#define SAG_PIN                                  1          
#define SAG_EMU                                  2
#define USE_SAG_TYPE                             SAG_NULL      ///@配置SAG类型

/* 硬件配置: 外部存储相关 */
#define USE_EEPROM            	                 1             ///@配置是否使用外部EEPROM
#define USE_DATAFLASH         	                 1             ///@配置是否使用外部DATAFLASH

/* 硬件配置: 键盘、按键、磁场开关相关 */
#define USE_KEYBOARD                             0             ///@配置是否使用键盘
#define USE_BTN_DISP_DN                          1             ///@配置是否使用下翻显示按键->往下翻，一般是两个按键的上面一个按键,或者作为单个按键存在
#define USE_BTN_DISP_UP                          1             ///@配置是否使用上翻显示按键->往上翻，一般是两个按键的下面一个按键
#define USE_BTN_BACK                             1             ///@配置是否使用返回按键->返回上一级菜单
#define USE_BTN_ENTER                            1             ///@配置是否使用确认按键->进入下一级菜单
#define USE_FCOVER                               0             ///@配置是否使用开面盖检测
#define USE_TCOVER                               0             ///@配置是否使用开端盖检测
#define USE_MAG_DST                              0             ///@配置是否使用磁场检测

/* 硬件配置: LED指示灯相关 */
#define USE_LED_WPULSE                           1             ///@配置是否使用有功脉冲灯
#define USE_LED_VARPULSE                         1             ///@配置是否使用无功脉冲灯
#define USE_LED_VAPULSE                          0             ///@配置是否使用视在脉冲灯
#define USE_LED_BACK                             1             ///@配置是否使用LCD背光灯
#define USE_LED_RELAY                            0             ///@配置是否使用报警灯-跳闸灯
#define USE_BUZZER                               0             ///@配置是否使用蜂鸣器

/* 硬件配置: LCD显示相关 */
#define USE_LCD                                  1             ///@配置是否使用LCD
#define USE_LCD_EXT_DRIVER                       1             ///@配置是否使用外部LCD驱动
///@brief LCD驱动选择
#define LCD_DRV_NONE                             0                  ///@无LCD驱动
#define LCD_DRV_MCU                              1                  ///@MCU自带LCD驱动
#define LCD_DRV_BL55080                          2                  ///@贝岭BL55080驱动
#define LCD_DRV_TYPE                             LCD_DRV_BL55080    ///@配置LCD驱动类型定义

///@brief LCD型号
#define LCD_NONE                                 0
#define LCD_JSH209105Y                           1              ///@国网表型号(8COM, 5V)
#define LCD_MK8702CFSP                           2              ///@MK8702CFSP型号(8COM, 3.3V)
#define LCD_TYPE                                 LCD_MK8702CFSP ///@配置LCD类型定义

/* 硬件配置: 信号检测相关 */
#define USE_ADC_INBAT                            1             ///@配置是否使用ADC采样内部电池
#define USE_ADC_EXBAT                            1             ///@配置是否使用ADC采样外部电池
#define USE_ADC_TEMPR                            1             ///@配置是否使用ADC采样温度

/* 硬件配置: 继电器控制相关*/
///@只有通道1支持双稳态输出，通道2、3仅支持单稳态输出，否则需修改relay.c文件中相关代码
#if HW_RELAY_INSIDE  
///@此宏主要针对国网表开关内置-外置，在工程中配置，其他表按需定义下面宏就可以,在工程中配置
#define USE_RLY_CTRL1                            1             ///@配置是否使用继电器1控制电路(内置继电器) 
#define USE_RLY_CTRL2                            0             ///@配置是否使用继电器2控制电路(外置继电器)
#else
#define USE_RLY_CTRL1                            0             ///@配置是否使用继电器1控制电路(内置继电器)
#define USE_RLY_CTRL2                            0             ///@配置是否使用继电器2控制电路(外置继电器)
#endif
#define USE_RLY_CTRL3                            0             ///@配置是否使用继电器3控制电路(报警继电器，有则开启，无则关闭)
#define USE_RLY_CHK                              0             ///@配置是否使用内置继电器检测电路
#define USE_RLY_CHK_EX                           0             ///@配置是否使用外置继电器检测电路
#define USE_RLY                                  (USE_RLY_CTRL1 || USE_RLY_CTRL2 || USE_RLY_CTRL3) ///@配置是否使用继电器

/* 硬件配置: UART通信端口 */
/// 不使用的通道必须注释,至少要打开一个通道
// #define COM_IR                                   HAL_UART2     ///@连接红外通讯
#define COM_RS4851                               HAL_UART1     ///@连接RS232/RS4851通讯
// #define COM_RS4852                               HAL_UART4     ///@连接RS232/RS4852通讯
// #define COM_7816                                 HAL_UART4     ///@连接7816 card
#define COM_MODULE                               HAL_UART0     ///@外置GPRS/载波模块通讯
// #define COM_BLE                                  HAL_UART0     ///@内置蓝牙模块通讯


/* 硬件配置: SPI通信端口 */
//HAL_SPI0      0    //MCU硬件SPI
//HAL_SPI1      1    //MCU硬件SPI
//HAL_SPI_SW0   SW0  //软件SPI0
//HAL_SPI_SW1   SW1  //软件SPI1
#define COM_DATAFLASH                            HAL_SPI0      ///@连接外部DATAFLASH芯片
#define COM_MEASURE_IC                           HAL_SPI1      ///@连接计量芯片


/* 硬件配置: I2C通信端口 */
#define COM_EEPROM                               HAL_IIC       ///@连接外部EEPROM芯片

/* 硬件配置: ADC采样通道分配 */
#if USE_ADC_INBAT
#define ADC_CHN_INBAT                            HAL_ADC_CHN1  ///@内部电池电压采样通道
#endif
#if USE_ADC_EXBAT
#define ADC_CHN_EXBAT                            HAL_ADC_CHN0  ///@外部电池电压采样通道
#endif

/* 用于电表磁场检测 */
#if USE_MAG_DST
#define IS_IO_MG_ACT()                           (!gpio_input_get(PIN_HALL_CHK))         // 获取磁场Magnetic状态
#endif

/* 用于按键状态检测 */
///开表盖
#if USE_FCOVER
#define EXTI_PIN_FCOVER                          TYPE_EXTI0                         // 面盖按键中断输入
#define IO_BTN_FCOVER_GET()                      gpio_input_get(PIN_METER_COVER)    // 获取面盖输入电平
#define IS_IO_BTN_FCOVER_OPEN()                  (IO_BTN_FCOVER_GET())              // 获取面盖开盖状态，高电平为开盖
#endif
//开端盖
#if USE_TCOVER
#define EXTI_PIN_TCOVER                          TYPE_EXTI6                      ///@端盖按键中断输入
#define IO_BTN_TCOVER_GET()                      gpio_input_get(PIN_TEM_COVER)   ///@获取端盖输入电平
#define IS_IO_BTN_TCOVER_OPEN()                  (IO_BTN_TCOVER_GET())           ///@获取端盖开盖状态，高电平为开盖
#endif
//上翻显示按键
#if USE_BTN_DISP_DN
#define EXTI_PIN_KEY_DN_BTN                      TYPE_PB04                    // 下翻显示按键中断输入
#define IO_BTN_DISP_DN_GET()                     gpio_input_get(PIN_KEY1)     // 获取下翻显示按键状态
#define IS_IO_BTN_DISP_DN_PRESS()                (!IO_BTN_DISP_DN_GET())      // 低电平为下翻显示按键按下
#endif
//下翻显示按键
#if USE_BTN_DISP_UP
#define EXTI_PIN_KEY_UP_BTN                      TYPE_PB05                    // 上翻显示按键中断输入
#define IO_BTN_DISP_UP_GET()                     gpio_input_get(PIN_KEY2)     // 获取上翻显示按键状态
#define IS_IO_BTN_DISP_UP_PRESS()                (!IO_BTN_DISP_UP_GET())      // 低电平为上翻显示按键按下
#endif



/* 用于电表电能脉冲灯 */
#if USE_LED_WPULSE
#define LED_ACTEGY_PULSE_ON()                    // 有功脉冲灯点亮
#define LED_ACTEGY_PULSE_OFF()	                 // 有功脉冲灯熄灭
#endif

#if USE_LED_VARPULSE
#define LED_REAEGY_PULSE_ON()                    // 无功脉冲灯点亮
#define LED_REAEGY_PULSE_OFF()                   // 无功脉冲灯熄灭
#endif

#if USE_LED_VAPULSE
#define LED_APPEGY_PULSE_ON()                    // 视在脉冲灯点亮
#define LED_APPEGY_PULSE_OFF()	                 // 视在脉冲灯熄灭
#endif

/* 用于LCD背光灯 */
#if USE_LED_BACK
#define LED_BACK_ON()                            gpio_out_H(PIN_LCD_BG)   // 点亮LCD背光灯
#define LED_BACK_OFF()                           gpio_out_L(PIN_LCD_BG)   // 熄灭LCD背光灯
#define LED_BACK_FLASH()                         gpio_out_rev(PIN_LCD_BG) // 闪烁LCD背光灯
#else
#define LED_BACK_ON()
#define LED_BACK_OFF()
#define LED_BACK_FLASH()
#endif

/* 用于电表跳闸指示灯 */
#if USE_LED_RELAY
#define LED_RELAY_ON()                           gpio_out_H(PIN_RELAY_LED)       // 点亮跳闸灯 
#define LED_RELAY_OFF()                          gpio_out_L(PIN_RELAY_LED)       // 熄灭跳闸灯
#define LED_RELAY_FLASH()                        gpio_out_rev(PIN_RELAY_LED)     // 闪烁跳闸灯
#else
#define LED_RELAY_ON()
#define LED_RELAY_OFF()
#define LED_RELAY_FLASH()
#endif

/// 外部通讯模块复位
#if defined(PIN_EX_MOD_RST)
#define IO_MOD_RST_SET()                             // 模块正常运行
#define IO_MOD_RST_RST()                             // 复位模块
#else
#define IO_MOD_RST_SET()
#define IO_MOD_RST_RST()
#endif

#if defined(PIN_MOD_EVT)
#define IO_MOD_EVT_SET()                             // 通知模块事件
#define IO_MOD_EVT_RST()                             // 取消模块事件
#else
#define IO_MOD_EVT_SET()
#define IO_MOD_EVT_RST()
#endif

/* 用于电表内部模块复位 */
#if defined(PIN_IN_MODULE_RST)
#define IO_INT_MOD_RST_SET()                       // 内部模块正常运行
#define IO_INT_MOD_RST_RST()                       // 复位内部模块
#else
#define IO_INT_MOD_RST_SET()
#define IO_INT_MOD_RST_RST()
#endif

#if defined(PIN_4G_POWER)
#define IO_4G_POWER_ON()                         gpio_out_H(PIN_4G_POWER)    // 4G模块上电
#define IO_4G_POWER_OFF()                        gpio_out_L(PIN_4G_POWER)    // 4G模块断电
#else   
#define IO_4G_POWER_ON()
#define IO_4G_POWER_OFF()
#endif


#if defined(PIN_4G_PWRKEY)
#define IO_4G_PWRKEY_START()                       gpio_out_H(PIN_4G_PWRKEY)    // 4G模块开机起始
#define IO_4G_PWRKEY_END()                         gpio_out_L(PIN_4G_PWRKEY)    // 4G模块开机结束
#else
#define IO_4G_PWRKEY_ON()
#define IO_4G_PWRKEY_OFF()
#endif

#if defined(PIN_4G_RST)
#define IO_4G_RST_START()                          gpio_out_H(PIN_4G_RST)       // 4G模块复位
#define IO_4G_RST_END()                            gpio_out_L(PIN_4G_RST)       // 4G模块正常运行
#else
#define IO_4G_RST_START()
#define IO_4G_RST_END()
#endif

#if defined(PIN_4G_WAKEUP)
#define IO_4G_WAKEUP_ON()                         gpio_out_L(PIN_4G_WAKEUP)    // 4G模块唤醒
#define IO_4G_WAKEUP_OFF()                        gpio_out_H(PIN_4G_WAKEUP)    // 4G模块休眠
#else
#define IO_4G_WAKEUP_ON()
#define IO_4G_WAKEUP_OFF()
#endif

/* 用于电表内部模块休眠或者电源控制 */
#if defined(PIN_MODULE_SLEEP)
#define IO_MOD_SLEEP()                               // 模块断电/休眠
#define IO_MOD_RUNNING()                             // 模块上电/正常运行
#else
#define IO_MOD_SLEEP()                          
#define IO_MOD_RUNNING()
#endif

///@定义是否使用外部空开合检测
#define IO_IR_PWR_ON()                          gpio_out_L(PIN_IR_CTRL)    ///@控制红外电源开
#define IO_IR_PWR_OFF()                         gpio_out_H(PIN_IR_CTRL)    ///@控制红外电源关

///@控制计量芯片电源
#define IO_EMU_PWR_ON()                         gpio_out_L(PIN_EMU_CTL)   ///@控制计量芯片电源开
#define IO_EMU_PWR_OFF()                        gpio_out_H(PIN_EMU_CTL)   ///@控制计量芯片电源关

///@控制蓝牙，flash电源
#define IO_POW3V3_ON()                          gpio_out_L(PIN_POWER_CTRL)    ///@控制蓝牙电源开
#define IO_POW3V3_OFF()                         gpio_out_H(PIN_POWER_CTRL)    ///@控制蓝牙电源关


/* 用于电表主继电器控制与检测 */
#if USE_RLY_CTRL1
/// 合闸
#define RLY_1_CLOSE_CLR()                        gpio_out_L(PIN_RELAY_ON)
#define RLY_1_CLOSE_SET()                        gpio_out_H(PIN_RELAY_ON)
/// 拉闸
#define RLY_1_OPEN_CLR()                         gpio_out_L(PIN_RELAY_OFF)
#define RLY_1_OPEN_SET()                         gpio_out_H(PIN_RELAY_OFF)
#if USE_RLY_CHK
#define IS_RLY_1_CLOSE_ON()                      !gpio_input_get(PIN_RLY_CHK)    // 主继电器1合闸检测
#endif
#endif



#if USE_RLY_CTRL2
/// 合闸
#define RLY_2_CLOSE_CLR()
#define RLY_2_CLOSE_SET()                        gpio_out_H(PIN_EXT_RELAY)
/// 拉闸
#define RLY_2_OPEN_CLR()
#define RLY_2_OPEN_SET()                         gpio_out_L(PIN_EXT_RELAY)
#if USE_RLY_CHK_EX
#define IS_RLY_2_CLOSE_ON()                      1
#endif
#endif

#if USE_RLY_CTRL3
/// 闭合
#define RLY_3_CLOSE_CLR()
#define RLY_3_CLOSE_SET()                        gpio_out_H(PIN_ALARM_RELAY)
/// 断开
#define RLY_3_OPEN_CLR()
#define RLY_3_OPEN_SET()                         gpio_out_L(PIN_ALARM_RELAY)
#endif


/* 用于电表蜂鸣器控制 */
#if USE_BUZZER
#define BEEP_ON(n)                               hal_timer.pwm_out(PWM_TIMER0, 4000 + n*100)
#define BEEP_OFF()                               hal_timer.pwm_out(PWM_TIMER0, 0)
#else
#define BEEP_ON(n)
#define BEEP_OFF()
#endif


/* 其它硬件驱动相关宏定义 */
#define STOP()                	                 hal_mcu.sleep()

#endif /* __BSP_CFG_H */

